export default {
    async fetch(request, env) {
      const url = new URL(request.url);
  
      // 验证 API 密钥
      // const apiKey = request.headers.get('X-API-Key');
      // if (apiKey !== env.API_KEY) {
      //   return new Response('Unauthorized', { status: 401 });
      // }
      // 处理批量操作
      if (url.pathname === '/batch') {
        return this.handleBatch(request, env);
      }
  
      // 处理 SELECT 查询
      if (url.pathname === '/query') {
        return this.handleQuery(request, env);
      }
  
      // 处理 INSERT/UPDATE/DELETE 操作
      if (url.pathname === '/execute') {
        return this.handleExecute(request, env);
      }
  
      // 默认返回 404
      return new Response('Not Found', { status: 404 });
    },
  
    // 处理 SELECT 查询
    async handleQuery(request, env) {
      try {
        const requestBody = await request.json();
        const { query, params } = requestBody;
  
        // 检查 query 是否存在
        if (!query) {
          return new Response(JSON.stringify({ error: "Query is required" }), {
            status: 400,
            headers: { "Content-Type": "application/json" },
          });
        }
  
        // 执行带参数的查询
        const stmt = env.db.prepare(query);
        const result = params ? await stmt.bind(...params).all() : await stmt.all();
  
        return new Response(JSON.stringify(result), {
          headers: { "Content-Type": "application/json" },
        });
      } catch (error) {
        return new Response(JSON.stringify({ error: error.message }), {
          status: 500,
          headers: { "Content-Type": "application/json" },
        });
      }
    },
  
    // 处理 INSERT/UPDATE/DELETE 操作
    async handleExecute(request, env) {
      try {
        const requestBody = await request.json();
        const { query, params } = requestBody;
  
        // 检查 query 是否存在
        if (!query) {
          return new Response(JSON.stringify({ error: "Query is required" }), {
            status: 400,
            headers: { "Content-Type": "application/json" },
          });
        }
  
        // 执行带参数的操作
        const stmt = env.db.prepare(query);
        const result = params ? await stmt.bind(...params).run() : await stmt.run();
  
        return new Response(JSON.stringify({ success: true, result }), {
          headers: { "Content-Type": "application/json" },
        });
      } catch (error) {
        return new Response(JSON.stringify({ error: error.message }), {
          status: 500,
          headers: { "Content-Type": "application/json" },
        });
      }
    },
    async handleBatch(request, env) {
      try {
        const requestBody = await request.json();
        const { queries } = requestBody;
  
        // 检查 queries 是否为数组且不为空
        if (!Array.isArray(queries) || queries.length === 0) {
          return new Response(JSON.stringify({ error: "Queries array is required" }), {
            status: 400,
            headers: { "Content-Type": "application/json" },
          });
        }
  
        // 修正：正确使用 D1 的 batch API
        const statements = queries.map(query => env.db.prepare(query));
        const results = await env.db.batch(statements);
  
        return new Response(JSON.stringify({ 
          success: true, 
          results 
        }), {
          headers: { "Content-Type": "application/json" },
        });
      } catch (error) {
        return new Response(JSON.stringify({ error: error.message }), {
          status: 500,
          headers: { "Content-Type": "application/json" },
        });
      }
    },
  };