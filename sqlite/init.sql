

-- 创建 reference_keywords 表
CREATE TABLE reference_keywords (
    id INTEGER,
    keyword TEXT,
    is_active INTEGER DEFAULT 1,
    created_at INTEGER,
    updated_at INTEGER,
    daily_search_volume INTEGER
);

-- 创建 rising_keywords 表
CREATE TABLE rising_keywords (
    id INTEGER,
    task_id TEXT,
    keyword TEXT,
    root_keyword TEXT,
    reference_keyword TEXT,
    keyword_interest_over_time TEXT,
    reference_keyword_interest_over_time TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    keyword_class TEXT,
    absolute_search_volume INTEGER,
    keyword_interest_over_time_absolute_search_volume TEXT
);

-- 创建 root_keywords 表
CREATE TABLE root_keywords (
    id INTEGER,
    keyword TEXT,
    status INTEGER DEFAULT 1,
    created_at INTEGER,
    updated_at INTEGER,
    pool_status TEXT DEFAULT 'newborn',
    parent_id INTEGER,
    date_added INTEGER
);

-- 创建 system_configs 表
CREATE TABLE system_configs (
    id INTEGER,
    config_key TEXT,
    config_value TEXT,
    description TEXT,
    created_at INTEGER,
    updated_at INTEGER
);

-- 创建 tasks 表
CREATE TABLE tasks (
    id TEXT,
    status TEXT,
    started_at INTEGER,
    completed_at INTEGER,
    error_message TEXT,
    created_at INTEGER
);

CREATE TABLE blacklist_words (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    word TEXT NOT NULL UNIQUE,
    is_active BOOLEAN NOT NULL DEFAULT 1,
    created_at TEXT
);
CREATE TABLE word_game_list (
    id INTEGER,
    word TEXT,
    game_name TEXT,
    date DATE
);

-- 创建 website 表
CREATE TABLE website (
    id INTEGER,
    site_id TEXT,
    site_name TEXT,
    url TEXT,
    method TEXT,
    game_name_extract_re TEXT
);