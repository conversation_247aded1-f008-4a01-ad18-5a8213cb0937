# Google Trends 数据分析项目

## 项目概述
这是一个用于分析Google Trends数据的自动化处理系统。项目主要用于监控和分析关键词的搜索趋势，通过代理轮转机制获取数据，并将结果存储到数据库中。

## 主要功能
1. 关键词趋势分析
   - 获取关键词的搜索兴趣度数据
   - 分析关键词的上升趋势
   - 计算关键词分类（A/B/C类）

2. 代理管理
   - 支持多代理轮转
   - 自动切换代理IP
   - 失败重试机制

3. 数据存储
   - 使用远程数据库存储分析结果
   - 支持批量数据处理
   - 数据持久化

4. 通知系统
   - 飞书机器人通知
   - 异常情况提醒

## 项目结构
```
process/
├── main.py          # 主程序入口
├── db.py            # 数据库操作模块
├── tasks.py         # 任务管理模块
├── trendspy2/       # Google Trends API封装
└── logs/            # 日志文件目录
```

## 配置说明
项目使用以下配置项：
- `rising_threshold`: 上升趋势阈值
- `max_retries`: 最大重试次数
- `initial_wait`: 初始等待时间
- `max_wait`: 最大等待时间

## 使用说明
1. 确保已安装所需依赖
2. 配置代理列表
3. 设置数据库连接
4. 运行主程序

## 依赖项
- pandas
- requests
- pytz
- trendspy2

## 注意事项
- 请确保代理IP可用
- 注意API调用频率限制
- 定期检查日志文件

## 数据库查询方法

### 1. 基本查询方法
项目使用 `DBWorker` 类进行数据库操作，提供两种主要查询方法：

```python
# 初始化数据库连接
worker = DBWorker(base_url="https://worker-db-proxy-trends.jameschan617.workers.dev")

# 方法1：execute - 执行SQL查询并返回原始结果
result = worker.execute("SELECT * FROM root_keywords")

# 方法2：execute_to_df - 执行SQL查询并返回DataFrame
df = worker.execute_to_df("SELECT * FROM root_keywords")
```

### 2. 常用查询示例

#### 2.1 查询根关键词
```python
# 获取所有根关键词
df = worker.execute_to_df("SELECT * FROM root_keywords")
```

返回结果示例：
```
   id          keyword  status  created_at  updated_at
0   1  example_keyword       1  1234567890  1234567890
1   2  another_keyword       1  1234567890  1234567890
```

#### 2.2 查询上升关键词
```python
# 获取特定任务的上升关键词
df = worker.execute_to_df("SELECT keyword FROM rising_keywords WHERE task_id='task_123'")
```

返回结果示例：
```
   keyword
0  rising_keyword1
1  rising_keyword2
```

#### 2.3 查询系统配置
```python
# 获取特定配置项
df = worker.execute_to_df("SELECT config_value FROM system_configs WHERE config_key='rising_threshold'")
```

返回结果示例：
```
   config_value
0          1000
```

### 3. 数据更新示例

```python
# 更新关键词数据
params = [
    "100,200,300",  # keyword_interest_over_time
    "50,100,150",   # reference_keyword_interest_over_time
    "reference_keyword",
    "A",            # keyword_class
    "task_123",     # task_id
    "root_keyword"  # root_keyword
]

result = worker.execute("""
    UPDATE rising_keywords 
    SET keyword_interest_over_time = ?,
        reference_keyword_interest_over_time = ?,
        reference_keyword = ?,
        keyword_class = ?
    WHERE task_id = ? 
    AND keyword = ?
""", params)
```

返回结果示例：
```json
{
    "success": true,
    "result": {
        "meta": {
            "changes": 1,
            "last_row_id": 123,
            "rows_read": 1,
            "rows_written": 1,
            "duration": 0.002
        }
    }
}
```

### 4. 注意事项
- 所有查询都支持参数化，建议使用参数化查询防止SQL注入
- 查询结果会自动转换为DataFrame格式（使用execute_to_df方法时）
- 更新操作会返回影响的行数和执行时间等元数据
- 建议使用try-except处理查询异常
