# 项目上下文信息

- 数据库信息：D1数据库，名称proxy-trends-d1，ID：cafc9cbe-9dc2-4afd-8a41-059556631167，新字段设置为可空以避免添加字段时的问题
- 用户需要详细了解三池管理的审查制度，包括什么时候查库、什么时候请求Google数据，要求形成清晰的文档说明
- 用户关心三池审查的具体细节：1)现在只有A级词进入三池 2)两个审查过程是否都需要查最新Google数据及必要性 3)审查时是否使用4词+基准词的批量查询方式
- 用户确认三池审查机制合理（基于历史数据，不请求Google API），现在需要定时任务和具体命令
- 用户提醒审查任务应该也有日任务，需要重新确认三池审查的执行频率
- 发现新问题：1)批次大小变成了1而不是4 2)出现数据类型错误 invalid literal for int() with base 10: '0.0' 3)关键词通过四层筛选但保存失败
