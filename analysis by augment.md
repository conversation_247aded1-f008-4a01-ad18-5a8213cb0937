
项目结构分析报告
🎯 主流程文件（核心业务逻辑）
以 process/main.py 为入口的主要业务流程包含以下文件：

1. 主入口文件
process/main.py - 主程序入口，协调整个数据获取和分析流程
2. 核心业务模块
process/tasks.py - 任务管理模块，负责创建、启动、完成任务的生命周期管理
process/db_ops.py - 数据库操作封装，提供高级数据库操作接口
process/trends_api.py - Google Trends API封装，获取关键词相关查询和兴趣度数据
process/analysis.py - 数据分析模块，处理兴趣度数据并进行关键词分类
process/utils.py - 工具函数模块，提供日志设置、时间计算等通用功能
process/notifier.py - 通知模块，发送飞书消息和任务完成通知
3. 基础设施模块
process/db.py - 数据库连接器，提供底层数据库访问功能
process/proxy.py - 代理管理模块，管理代理IP轮换和评分
process/trendspy2/ - Google Trends API客户端库（第三方库的本地版本）
🧪 测试和开发文件（非主流程）
1. 测试文件
process/test.py - TrendsAnalyzer类的测试文件，包含API测试逻辑
process/test2.py - 简单的trends API测试脚本
2. 备份和历史文件
process/main copy.py.bak - 主程序的备份版本，包含旧的实现逻辑
3. 工具脚本
process/retry.py - 重试失败任务的工具脚本
process/word_game.py - Wordle答案抓取工具，用于获取游戏答案数据
4. 配置和部署文件
utils/dbworker.js - Cloudflare Worker数据库代理服务
sqlite/init.sql - 数据库初始化脚本
requirements.txt - Python依赖包列表
setup.py - 项目安装配置
5. 日志和缓存文件
process/logs/ - 日志文件目录
process/__pycache__/ - Python字节码缓存