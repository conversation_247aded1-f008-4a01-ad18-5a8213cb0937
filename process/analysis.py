import logging
from notifier import send_feishu_message
from db_ops import save_interest_over_time_to_db, get_reference_word, get_config_value

def calculate_keyword_class(keyword_data, reference_data, absolute_volume_series, reference_keyword):
    """四层筛选模型计算关键词分类

    根据四层筛选标准判断关键词是否符合爆发性增长模式：
    1. 基线标准 (相对基线): 目标关键词前3天平均值 / 基准词前3天平均值 < baseline_threshold
    2. 增长标准 (爆炸性相对增长): 目标关键词峰值 / 目标关键词基线值 > growth_threshold
    3. 时效性标准 (当下顶峰): 峰值在第7天 OR 第7天数据≥峰值*timeliness_threshold
    4. 超越标准 (超越基准): 第7天目标关键词热度 > 第7天基准词热度

    配置参数（从system_configs表读取）：
    - baseline_threshold: 基线标准阈值，默认0.05
    - growth_threshold: 增长标准倍数，默认10（1000%）
    - timeliness_threshold: 时效性标准百分比，默认0.9（90%）

    Args:
        keyword_data: 关键词搜索量数据 (7天数据)
        reference_data: 参考关键词搜索量数据 (7天数据)
        absolute_volume_series: 绝对搜索量序列字符串
        reference_keyword: 基准词名称

    Returns:
        str or None: 关键词分类，符合四层筛选返回'A'，否则返回None
    """
    # 将字符串转回数值列表用于计算，保留1位小数精度
    keyword_data_list = [round(float(x), 1) for x in keyword_data]
    reference_data_list = [round(float(x), 1) for x in reference_data]

    # 确保数据长度为7天
    if len(keyword_data_list) < 7 or len(reference_data_list) < 7:
        logging.warning(f"数据长度不足7天: keyword={len(keyword_data_list)}, reference={len(reference_data_list)}")
        return None

    # 第一层：基线标准 (绝对搜索量基线)
    # 使用绝对搜索量计算基线，避免标准化问题
    baseline_threshold = float(get_config_value('baseline_threshold', 0.05))

    # 计算绝对搜索量的前3天平均值
    absolute_volume_list = [float(x) for x in absolute_volume_series.split(',')]
    keyword_absolute_first_3_avg = sum(absolute_volume_list[:3]) / 3

    # 获取基准词参考流量
    reference_df = get_reference_word()
    reference_traffic = None
    for _, row in reference_df.iterrows():
        if row['keyword'] == reference_keyword:
            reference_traffic = row.get('daily_search_volume')
            break

    # 计算基线比：目标关键词前3天绝对搜索量平均值 / 基准词参考流量
    if reference_traffic == 0:
        logging.warning("基准词参考流量为0，无法计算基线标准")
        return None

    baseline_ratio = keyword_absolute_first_3_avg / reference_traffic
    if baseline_ratio >= baseline_threshold:
        logging.debug(f"未通过基线标准: {baseline_ratio:.4f} >= {baseline_threshold}")
        return None

    # 第二层：增长标准 (爆炸性相对增长)
    # 目标关键词峰值 / 目标关键词基线值 > 配置倍数
    growth_threshold = float(get_config_value('growth_threshold', 10))  # 默认1000% = 10倍

    keyword_peak = max(keyword_data_list)
    keyword_baseline = sum(keyword_data_list[:3]) / 3

    if keyword_baseline == 0:
        # 如果基线为0，使用最小非零值作为基线
        non_zero_values = [x for x in keyword_data_list[:3] if x > 0]
        if not non_zero_values:
            keyword_baseline = 0.01  # 使用极小值避免除零
        else:
            keyword_baseline = min(non_zero_values)

    growth_ratio = keyword_peak / keyword_baseline
    if growth_ratio <= growth_threshold:
        logging.debug(f"未通过增长标准: {growth_ratio:.2f} <= {growth_threshold}")
        return None

    # 第三层：时效性标准 (当下顶峰)
    # 峰值在第7天 OR 第7天数据≥峰值配置百分比
    timeliness_threshold = float(get_config_value('timeliness_threshold', 0.9))  # 默认90%

    day_7_value = keyword_data_list[6]  # 第7天数据
    peak_threshold_value = keyword_peak * timeliness_threshold

    is_peak_on_day_7 = (day_7_value == keyword_peak)
    is_day_7_near_peak = (day_7_value >= peak_threshold_value)

    if not (is_peak_on_day_7 or is_day_7_near_peak):
        logging.debug(f"未通过时效性标准: day7={day_7_value}, peak={keyword_peak}, {timeliness_threshold*100}%peak={peak_threshold_value:.1f}")
        return None

    # 第四层：超越标准 (超越基准)
    # 第7天目标关键词热度 > 第7天基准词热度
    reference_day_7 = reference_data_list[6]
    if day_7_value <= reference_day_7:
        logging.debug(f"未通过超越标准: keyword_day7={day_7_value} <= reference_day7={reference_day_7}")
        return None

    # 通过所有四层筛选，返回A级分类
    logging.info(f"通过四层筛选: 基线比={baseline_ratio:.4f}, 增长比={growth_ratio:.2f}, "
                f"第7天={day_7_value}, 峰值={keyword_peak}, 基准第7天={reference_day_7}")
    return 'A'

def calculate_absolute_search_volume_series(keyword_data, reference_data, reference_keyword):
    """计算关键词的绝对搜索量时间序列

    基于基准词的参考流量和相对比例计算目标关键词的绝对搜索量时间序列

    Args:
        keyword_data: 关键词搜索量数据 (相对值)
        reference_data: 参考关键词搜索量数据 (相对值)
        reference_keyword: 参考关键词名称

    Returns:
        str: 关键词的绝对搜索量时间序列，格式如 "100,26,26,89,85,85,84,63"
    """
    try:
        # 获取基准词的参考流量
        reference_df = get_reference_word()
        reference_traffic = None

        for _, row in reference_df.iterrows():
            if row['keyword'] == reference_keyword:
                reference_traffic = row.get('daily_search_volume')
                break

        if reference_traffic is None:
            logging.warning(f"未找到基准词 '{reference_keyword}' 的参考流量，使用默认值4k")
            reference_traffic = 4

        # 将字符串转回数值列表，保留1位小数精度
        keyword_data_list = [round(float(x), 1) for x in keyword_data]
        reference_data_list = [round(float(x), 1) for x in reference_data]

        logging.debug(f"相对搜索量计算: keyword_data={keyword_data_list}, reference_data={reference_data_list}")

        # 计算基准词7天平均值，减少误差
        reference_7day_avg = sum(reference_data_list) / len(reference_data_list) if reference_data_list else 1
        if reference_7day_avg == 0:
            reference_7day_avg = 0.1  # 避免除零

        logging.debug(f"基准词7天平均值: {reference_7day_avg:.1f}")

        # 计算每个时间点的绝对搜索量
        absolute_series = []

        for i in range(len(keyword_data_list)):
            keyword_value = keyword_data_list[i]

            # 计算绝对搜索量: (关键词值 / 基准词7天平均值) * 基准词参考流量
            ratio = keyword_value / reference_7day_avg
            absolute_value = round(ratio * reference_traffic, 1)  # 保留1位小数
            absolute_series.append(str(absolute_value))

        result = ','.join(absolute_series)

        logging.debug(f"绝对搜索量序列计算: reference_traffic={reference_traffic}k, "
                     f"keyword_peak={max(keyword_data_list)}, reference_7day_avg={reference_7day_avg:.1f}, "
                     f"result={result}")

        return result

    except Exception as e:
        logging.error(f"计算绝对搜索量序列时出错: {str(e)}")
        return "0,0,0,0,0,0,0"

def process_interest_data(interest_over_time_data, task_id, keyword, root_keyword, reference_keyword, test_mode=False):
    """处理兴趣度数据并保存到数据库

    Args:
        interest_over_time_data: 获取的兴趣度数据 [keyword_data, reference_data]
        task_id: 任务ID
        keyword: 关键词
        root_keyword: 根关键词
        reference_keyword: 参考关键词
        test_mode: 测试模式

    Returns:
        dict: 处理结果
    """
    try:
        # 计算绝对搜索量时间序列
        absolute_volume_series = calculate_absolute_search_volume_series(
            interest_over_time_data[0],
            interest_over_time_data[1],
            reference_keyword
        )

        # 计算关键词分类（使用绝对搜索量）
        keyword_class = calculate_keyword_class(
            interest_over_time_data[0],
            interest_over_time_data[1],
            absolute_volume_series,
            reference_keyword
        )

        # 如果关键词被分类，发送飞书消息（测试模式下跳过）
        if keyword_class:
            # 从绝对搜索量序列中提取峰值用于显示
            absolute_values = [int(float(x)) for x in absolute_volume_series.split(',')]
            peak_absolute_volume = max(absolute_values)
            logging.info(f"关键词 '{keyword}' 被分类为: {keyword_class}, 绝对搜索量峰值: {peak_absolute_volume}k")

            if not test_mode:
                send_feishu_message(keyword,root_keyword,reference_keyword)
            else:
                logging.info(f"测试模式：跳过发送飞书消息")

            # 如果关键词被分类为 A、B、C，自动添加到根关键词表（测试模式下跳过）
            if keyword_class in ['A', 'B', 'C'] and not test_mode:
                from db_ops import check_keyword_exists_in_root, add_keyword_to_root_with_pool_info
                try:
                    if not check_keyword_exists_in_root(keyword):
                        # 使用新的函数添加到根关键词表，包含三池信息
                        if add_keyword_to_root_with_pool_info(keyword, root_keyword):
                            logging.info(f"关键词 '{keyword}' 已自动添加到根关键词表(新生池)")
                        else:
                            logging.warning(f"关键词 '{keyword}' 添加到根关键词表失败")
                    else:
                        logging.info(f"关键词 '{keyword}' 已存在于根关键词表中，跳过添加")
                except Exception as e:
                    logging.error(f"处理关键词 '{keyword}' 自动入库时出错: {str(e)}")
            elif keyword_class in ['A', 'B', 'C'] and test_mode:
                logging.info(f"测试模式：跳过将关键词 '{keyword}' 添加到根关键词表")

        # 保存到数据库
        result = save_interest_over_time_to_db(
            interest_over_time_data,
            task_id,
            keyword,
            reference_keyword,
            keyword_class,
            absolute_volume_series,
            test_mode
        )

        return {
            "success": result.get("success", False),
            "keyword": keyword,
            "class": keyword_class
        }

    except Exception as e:
        logging.error(f"处理关键词 '{keyword}' 的兴趣度数据时出错: {str(e)}")

        # 即使处理失败，也要标记为已处理，避免无限重试
        try:
            # 使用简单的标记来表示处理失败
            from db_ops import worker
            worker.execute(
                """UPDATE rising_keywords
                   SET keyword_interest_over_time = 'ERROR',
                       keyword_interest_over_time_absolute_search_volume = 'ERROR'
                   WHERE task_id = ? AND keyword = ?""",
                [task_id, keyword]
            )
            logging.info(f"已标记关键词 '{keyword}' 为处理失败，避免重复处理")
        except Exception as save_error:
            logging.error(f"标记失败关键词 '{keyword}' 时出错: {str(save_error)}")

        return {
            "success": False,
            "keyword": keyword,
            "error": str(e)
        }

def process_keywords_batch(keywords_batch, task_id, reference_keyword, test_mode=False):
    """批量处理关键词的兴趣度数据

    Args:
        keywords_batch: 关键词批次列表，每个元素包含keyword和root_keyword
        task_id: 任务ID
        reference_keyword: 参考关键词
        test_mode: 测试模式

    Returns:
        dict: 批量处理结果统计
    """
    from trends_api import get_interest_over_time_batch

    if not keywords_batch:
        return {"success": True, "processed": 0, "successful": 0, "failed": 0}

    # 提取关键词列表
    keywords_list = [item['keyword'] for item in keywords_batch]

    try:
        # 批量获取兴趣度数据
        logging.info(f"开始批量处理{len(keywords_list)}个关键词")
        batch_data = get_interest_over_time_batch(keywords_list, reference_keyword)

        # 处理每个关键词的数据
        results = {
            "processed": len(keywords_list),
            "successful": 0,
            "failed": 0,
            "details": []
        }

        for item in keywords_batch:
            # 安全地获取关键词信息
            if not isinstance(item, dict):
                logging.error(f"批量处理项格式错误: {item}")
                results["failed"] += 1
                continue

            keyword = item.get('keyword')
            root_keyword = item.get('root_keyword')

            if not keyword or not root_keyword:
                logging.error(f"批量处理项缺少必要字段: {item}")
                results["failed"] += 1
                continue

            try:
                # 获取该关键词的数据
                keyword_data = batch_data.get(keyword)
                reference_data = batch_data.get(reference_keyword)

                if keyword_data is None or reference_data is None:
                    logging.warning(f"关键词 '{keyword}' 的数据不完整")
                    results["failed"] += 1
                    results["details"].append({
                        "keyword": keyword,
                        "success": False,
                        "error": "数据不完整"
                    })
                    continue

                # 处理单个关键词数据
                result = process_interest_data(
                    [keyword_data, reference_data],
                    task_id,
                    keyword,
                    root_keyword,
                    reference_keyword,
                    test_mode
                )

                if result.get('success', False):
                    results["successful"] += 1
                    keyword_class = result.get('class')
                    class_info = f"(分类: {keyword_class})" if keyword_class else "(未分类)"
                    logging.info(f"批量处理成功: '{keyword}' {class_info}")
                else:
                    results["failed"] += 1
                    logging.warning(f"批量处理失败: '{keyword}' - {result.get('error', '未知错误')}")

                results["details"].append(result)

            except Exception as e:
                results["failed"] += 1
                import traceback
                error_detail = traceback.format_exc()
                error_result = {
                    "keyword": keyword,
                    "success": False,
                    "error": str(e)
                }
                results["details"].append(error_result)
                logging.error(f"批量处理关键词 '{keyword}' 时出错: {str(e)}")
                logging.error(f"详细错误信息: {error_detail}")

        results["success"] = results["failed"] == 0
        logging.info(f"批量处理完成: 总计{results['processed']}个，成功{results['successful']}个，失败{results['failed']}个")

        return results

    except Exception as e:
        logging.error(f"批量处理关键词时出错: {str(e)}")
        return {
            "success": False,
            "processed": len(keywords_list),
            "successful": 0,
            "failed": len(keywords_list),
            "error": str(e),
            "details": []
        }

def normalize_interest_data(keyword_data, reference_data):
    """将天级数据标准化为0-100的范围

    在5个词的批量查询中，找到所有词所有天中的最高值，将其标准化为100

    Args:
        keyword_data: 关键词天级搜索量数据
        reference_data: 参考关键词天级搜索量数据

    Returns:
        tuple: 标准化后的(keyword_data, reference_data)，返回1位小数值
    """
    # 合并两组数据，找到所有数据中的最大值
    all_data = keyword_data + reference_data
    global_max = max(all_data)

    # 按最高值为100进行标准化，返回1位小数值
    if global_max > 0:
        normalized_keyword_data = [round(x * 100 / global_max, 1) for x in keyword_data]
        normalized_reference_data = [round(x * 100 / global_max, 1) for x in reference_data]
        return normalized_keyword_data, normalized_reference_data

    return keyword_data, reference_data

def aggregate_hourly_to_daily(hourly_data):
    """将小时级数据聚合为天级数据

    Args:
        hourly_data: 小时级数据列表

    Returns:
        list: 天级数据列表，返回整数值
    """
    daily_sums = []
    days = len(hourly_data) // 24

    for day in range(days):
        start_idx = day * 24
        end_idx = start_idx + 24
        # 计算每天的总搜索量，返回整数值
        daily_sums.append(int(round(sum(hourly_data[start_idx:end_idx]))))

    return daily_sums