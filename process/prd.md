# Google Trends 关键词挖掘系统 PRD

## 1. 基准词增强

### 1.1 基准词参考流量字段
- **字段名称**: `reference_traffic`
- **数据类型**: INTEGER
- **单位**: k (千)
- **说明**: 基准词的日均搜索流量
- **初始数据**: "animal generator" 赋值 4k

### 1.2 用途
- 用于计算其他关键词的绝对搜索量
- 作为三池处理中量化标准的基础

## 2. 四层筛选模型算法更新

### 2.1 基线标准 (相对基线)
- **目标**: 确保目标关键词的起点足够低
- **公式**: `目标关键词前3天平均值 / 基准词前3天平均值 < 0.05`
- **说明**: 目标关键词相对基准词的初始热度必须很低

### 2.2 增长标准 (爆炸性相对增长)
- **目标**: 确保目标关键词的增长足够迅猛
- **公式**: `目标关键词峰值 / 目标关键词基线值 > 1000%`
- **说明**: 目标关键词必须有超过10倍的增长

### 2.3 时效性标准 (当下顶峰)
- **目标**: 确保目标关键词热度在观察期末达到顶点
- **条件**: 满足以下任一条件即可
  - 目标关键词的数据峰值是第7天的数据
  - 第7天数据 ≥ 峰值的90% (`B_D7 >= B_peak * 0.9`)
- **说明**: 确保关键词在观察期结束时仍处于高热度状态

### 2.4 超越标准 (超越基准)
- **目标**: 确保目标关键词影响力超越了基准
- **公式**: `B_D7 > A_D7`
- **说明**: 目标关键词第7天的热度必须超过基准词第7天的热度

## 3. Rising Keywords 处理优化

### 3.1 批量查询机制
- **查询方式**: 5个词一起查询
- **组成**: 1个基准词 + 4个目标关键词
- **效率**: 每次可以查询4个新词

### 3.2 去重机制
- **同词根去重**: 不同词根的同一个新词，只查询一次
- **数据库去重**: 不同词根发现同一个新词，只插入一次
- **实现**: 在插入前检查关键词是否已存在

### 3.3 绝对搜索量计算
- **新增字段**: `keyword_interest_over_time_absolute_search_volume`
- **数据类型**: TEXT
- **单位**: k (千)
- **计算方式**: 基于基准词参考流量和相对比例计算绝对搜索量，
- **说明**:参考原来的字段，keyword_interest_over_time，是类似`100,26,26,89,85,85,84,63`的数列
## 4. 三池处理方案

### 4.1 词根表字段扩展
- **新增字段**: `pool_status`
- **可选值**:
  - `newborn` - 新生池
  - `stable` - 稳定池
  - `decayed` - 衰退池
- **新增字段**: `parent_id` - 用于追踪词根血缘关系
- **新增字段**: `date_added` - 进入当前池的时间

### 4.2 第一阶段：新生池 (Observation Pool)

#### 准入标准
- 通过四层筛选模型
- 在第七天达到顶峰的新词

#### 核心操作
1. **高频关联**: 进入新生池的前3天，作为最高优先级词根，每天高频次挖掘关联词
2. **表现追踪**: 持续追踪接下来3天的热度表现

#### 有效期
- **时长**: 3天
- **后续**: 3天后必须进行"晋升"或"降级"裁定

### 4.3 第二阶段：稳定池 (Stable Pool)

#### 晋升标准
- 3天观察期结束后，热度虽从峰值回落，但稳定在远高于初始基线的水平
- **量化判断**: 热度必须持续高于基准词

#### 核心操作
1. **常规关联**: 作为核心词根，以常规频率（每天一次）进行关联词搜索
2. **定期审查**: 每周或每两周检查一次热度状态

#### 审查标准
1. **7日均值审查**: 过去7天日均绝对搜索量 > 基准词参考流量的10倍 (40k)
2. **近期健康度审查**: 过去3天日均绝对搜索量 > 基准词参考流量的10倍 (40k)

### 4.4 第三阶段：衰退池 (Decay Pool / Archive)

#### 降级标准
1. **新生池降级**: 3天观察期后，热度断崖式下跌，几乎归零
2. **稳定池降级**: 审查中发现热度已消失

#### 核心操作
1. **停止关联**: 立即停止使用该词作为词根进行关联搜索
2. **存档标记**: 移动到衰退池或打上"已归档"标签，不直接删除

## 5. 删除逻辑与连坐机制

### 5.1 数据结构设计
- **parent_id字段**: 追踪词根血缘关系
- **连坐追踪**: 通过parent_id实现家族式管理

### 5.2 连坐降级逻辑

#### 触发条件
- 父词根表现不佳需要降级时

#### 执行步骤
1. 将父词根降级到衰退池
2. 找到所有子词根
3. 对每个子词根进行"独立生存测试"

### 5.3 独立生存测试

#### 测试标准
- **公式**: `子词根历史峰值绝对搜索量 > 父词根历史峰值绝对搜索量 * 1.2`
- **说明**: 子词根的峰值必须比父词根高出20%以上

#### 测试结果处理
1. **通过测试**:
   - 切断血缘关系（parent_id设为NULL）
   - 成为独立的初代词根
   - 按自己的节奏进行生命周期管理

2. **未通过测试**:
   - 认定为父词根的附属品
   - 一同降级到衰退池

### 5.4 稳定池独立化
- **操作**: 稳定池中的词根自动删除parent_id
- **目的**: 确保稳定词根不受原始发现路径影响

## 6. 自动化执行脚本

### 6.1 每日审查脚本
- **执行频率**: 每日自动执行
- **主要功能**:
  1. 审查新生池中观察期满的词根
  2. 执行晋升/降级判断
  3. 触发连坐机制处理

### 6.2 定期审查脚本
- **执行频率**: 每周或每两周
- **主要功能**:
  1. 审查稳定池中词根的表现
  2. 执行稳定池降级判断
  3. 清理衰退池中的过期数据

## 7. 实施优先级

### 7.1 第一阶段
1. 基准词参考流量字段添加
2. 四层筛选模型算法实现
3. Rising Keywords批量查询和去重机制

### 7.2 第二阶段
1. 三池处理数据结构设计
2. 新生池和稳定池逻辑实现
3. 绝对搜索量计算功能

### 7.3 第三阶段
1. 连坐机制和独立生存测试
2. 自动化审查脚本开发
3. 衰退池管理功能