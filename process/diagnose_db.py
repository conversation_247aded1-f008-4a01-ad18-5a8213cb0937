#!/usr/bin/env python3
"""
数据库诊断脚本

检查数据库表结构和字段是否正确
"""

from db_ops import worker

def check_table_structure():
    """检查表结构"""
    print("=== 检查 rising_keywords 表结构 ===")
    
    try:
        # 检查表结构
        df = worker.execute_to_df("PRAGMA table_info(rising_keywords)")
        
        print("字段列表:")
        for _, row in df.iterrows():
            print(f"  {row['name']} ({row['type']}) - {'NOT NULL' if row['notnull'] else 'NULL'}")
        
        # 检查是否有新字段
        field_names = df['name'].tolist()
        required_field = 'keyword_interest_over_time_absolute_search_volume'
        
        if required_field in field_names:
            print(f"✅ 字段 '{required_field}' 存在")
        else:
            print(f"❌ 字段 '{required_field}' 不存在")
            print(f"需要添加字段: ALTER TABLE rising_keywords ADD COLUMN {required_field} TEXT;")
        
    except Exception as e:
        print(f"检查表结构失败: {e}")

def check_recent_data():
    """检查最近的数据"""
    print("\n=== 检查最近的数据 ===")
    
    try:
        # 获取最近的几条记录
        df = worker.execute_to_df(
            """SELECT task_id, keyword, keyword_class, 
                      keyword_interest_over_time_absolute_search_volume,
                      created_at
               FROM rising_keywords 
               ORDER BY created_at DESC 
               LIMIT 5"""
        )
        
        if not df.empty:
            print("最近5条记录:")
            for _, row in df.iterrows():
                task_id = row['task_id']
                keyword = row['keyword']
                keyword_class = row['keyword_class']
                absolute_volume = row['keyword_interest_over_time_absolute_search_volume']
                created_at = row['created_at']
                
                print(f"  {task_id} | {keyword} | {keyword_class or '未分类'} | "
                      f"{'有绝对值' if absolute_volume else '无绝对值'} | {created_at}")
        else:
            print("没有找到数据")
            
    except Exception as e:
        print(f"检查数据失败: {e}")

def test_update():
    """测试更新操作"""
    print("\n=== 测试更新操作 ===")
    
    try:
        # 获取一条测试记录
        df = worker.execute_to_df(
            """SELECT task_id, keyword FROM rising_keywords 
               WHERE keyword_interest_over_time_absolute_search_volume IS NULL
               LIMIT 1"""
        )
        
        if not df.empty:
            task_id = df.iloc[0]['task_id']
            keyword = df.iloc[0]['keyword']
            
            print(f"测试更新记录: {task_id} | {keyword}")
            
            # 尝试更新
            result = worker.execute(
                """UPDATE rising_keywords 
                   SET keyword_interest_over_time_absolute_search_volume = ?
                   WHERE task_id = ? AND keyword = ?""",
                ["1,2,3,4,5,6,7", task_id, keyword]
            )
            
            if result.get('success', False):
                print("✅ 更新测试成功")
                
                # 验证更新
                verify_df = worker.execute_to_df(
                    """SELECT keyword_interest_over_time_absolute_search_volume 
                       FROM rising_keywords 
                       WHERE task_id = ? AND keyword = ?""",
                    [task_id, keyword]
                )
                
                if not verify_df.empty:
                    value = verify_df.iloc[0]['keyword_interest_over_time_absolute_search_volume']
                    print(f"✅ 验证成功，值为: {value}")
                else:
                    print("❌ 验证失败，未找到记录")
            else:
                print(f"❌ 更新测试失败: {result.get('error', '未知错误')}")
        else:
            print("没有找到可测试的记录")
            
    except Exception as e:
        print(f"测试更新失败: {e}")

if __name__ == "__main__":
    check_table_structure()
    check_recent_data()
    test_update()
