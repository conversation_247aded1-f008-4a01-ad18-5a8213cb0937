#!/usr/bin/env python3
"""
三池状态查看工具

用于查看和管理三池中的关键词状态

使用方法：
python pool_status.py stats           # 查看三池统计信息
python pool_status.py list newborn    # 查看新生池关键词
python pool_status.py list stable     # 查看稳定池关键词
python pool_status.py list decayed    # 查看衰退池关键词
"""

import sys
import argparse
from datetime import datetime
from db_ops import get_pool_statistics, get_keywords_by_pool_status

def format_timestamp(timestamp):
    """格式化时间戳"""
    if timestamp:
        return datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
    return "未知"

def show_pool_statistics():
    """显示三池统计信息"""
    print("=== 三池统计信息 ===")
    stats = get_pool_statistics()
    
    print(f"新生池 (newborn): {stats['newborn']} 个关键词")
    print(f"稳定池 (stable):  {stats['stable']} 个关键词")
    print(f"衰退池 (decayed): {stats['decayed']} 个关键词")
    print(f"总计:            {sum(stats.values())} 个关键词")

def show_pool_keywords(pool_status, limit=20):
    """显示指定池中的关键词"""
    pool_names = {
        'newborn': '新生池',
        'stable': '稳定池', 
        'decayed': '衰退池'
    }
    
    print(f"=== {pool_names.get(pool_status, pool_status)} 关键词列表 ===")
    
    keywords = get_keywords_by_pool_status(pool_status, limit)
    
    if not keywords:
        print("该池中没有关键词")
        return
    
    print(f"显示前 {min(len(keywords), limit)} 个关键词:")
    print()
    print(f"{'ID':<5} {'关键词':<30} {'进入时间':<20} {'父ID':<8}")
    print("-" * 70)
    
    for keyword_data in keywords:
        keyword_id = keyword_data['id']
        keyword = keyword_data['keyword']
        date_added = format_timestamp(keyword_data.get('date_added'))
        parent_id = keyword_data.get('parent_id', '')
        
        print(f"{keyword_id:<5} {keyword:<30} {date_added:<20} {parent_id or 'N/A':<8}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='三池状态查看工具')
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # stats 命令
    subparsers.add_parser('stats', help='查看三池统计信息')
    
    # list 命令
    list_parser = subparsers.add_parser('list', help='查看指定池的关键词列表')
    list_parser.add_argument('pool', choices=['newborn', 'stable', 'decayed'], 
                           help='池类型')
    list_parser.add_argument('--limit', '-l', type=int, default=20,
                           help='显示数量限制 (默认: 20)')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    try:
        if args.command == 'stats':
            show_pool_statistics()
        elif args.command == 'list':
            show_pool_keywords(args.pool, args.limit)
            
    except Exception as e:
        print(f"执行命令时出错: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
