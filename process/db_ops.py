import logging
from db import DBWorker

# 数据库连接配置
DB_WORKER_URL = "https://worker-db-proxy-trends.jameschan617.workers.dev"
worker = DBWorker(base_url=DB_WORKER_URL)

def get_root_keywords(test_mode=False, test_keywords=None):
    """获取根关键词列表（排除衰退池）"""
    if test_mode and test_keywords:
        # 测试模式：返回测试关键词数据
        import pandas as pd
        test_data = []
        for i, keyword in enumerate(test_keywords):
            test_data.append({
                'id': i + 1,
                'keyword': keyword,
                'pool_status': 'newborn',
                'created_at': '2024-01-01',
                'updated_at': '2024-01-01'
            })
        return pd.DataFrame(test_data)

    df = worker.execute_to_df(
        """SELECT * FROM root_keywords
           WHERE pool_status != 'decayed' OR pool_status IS NULL
           ORDER BY id DESC"""
    )
    return df

def get_rising_keywords(task_id):
    """获取特定任务的上升关键词"""
    df = worker.execute_to_df(f"select keyword,root_keyword from rising_keywords where task_id='{task_id}' ")
    keywords = df[['keyword','root_keyword']].to_dict(orient='records')  # 转换为Python列表
    return keywords

def get_config_value(config_key: str, default_value=None):
    """从系统配置表获取配置值"""
    df = worker.execute_to_df(f"select config_value from system_configs where config_key='{config_key}'")
    if df.empty:
        return default_value
    return df.iloc[0]['config_value']

def get_reference_word():
    """获取参考关键词"""
    df = worker.execute_to_df("select * from reference_keywords")
    return df

def get_rising_threshold():
    """获取上升阈值"""
    return int(get_config_value('rising_threshold', 1000))

def get_blacklist_words():
    """获取黑名单词汇列表"""
    df = worker.execute_to_df("SELECT word FROM blacklist_words WHERE is_active = 1")
    return [word.lower() for word in df['word'].tolist()]

def get_wordle_answer():
    """获取最近三天的Wordle答案，用于过滤"""
    df = worker.execute_to_df("select word from word_game_list where game_name='wordle' and date > date('now','-3 days');")
    return [word.lower() for word in df['word'].tolist()]

def save_interest_over_time_to_db(interest_over_time_data, task_id, keyword, reference_keyword, keyword_class=None, absolute_volume_series=None, test_mode=False):
    """保存关键词的兴趣度时间序列数据到数据库"""
    if test_mode:
        # 测试模式：跳过数据库保存，返回模拟结果
        logging.info(f"测试模式：模拟保存关键词 '{keyword}' 的兴趣度数据，分类={keyword_class}")
        return {"success": True, "message": "测试模式：跳过数据库保存"}

    try:
        # 将数据转换为1位小数后再转换为字符串以便存储，提高数据精度
        keyword_data = ','.join(str(round(float(x), 1)) for x in interest_over_time_data[0])
        reference_data = ','.join(str(round(float(x), 1)) for x in interest_over_time_data[1])

        # 处理绝对搜索量数据（可能包含小数）
        if absolute_volume_series:
            # 确保绝对搜索量数据格式正确（保留1位小数）
            absolute_values = [str(round(float(x), 1)) for x in absolute_volume_series.split(',')]
            absolute_volume_series = ','.join(absolute_values)

        # 准备更新数据库的参数
        params = [
            keyword_data,          # keyword_interest_over_time
            reference_data,        # reference_keyword_interest_over_time
            reference_keyword,
            keyword_class,
            absolute_volume_series, # keyword_interest_over_time_absolute_search_volume
            task_id,              # task_id
            keyword,         # root_keyword
        ]

        # 执行数据库更新操作
        result = worker.execute(
            """
            UPDATE rising_keywords
            SET keyword_interest_over_time = ?,
                reference_keyword_interest_over_time = ?,
                reference_keyword = ?,
                keyword_class = ?,
                keyword_interest_over_time_absolute_search_volume = ?
            WHERE task_id = ?
            AND keyword = ?
            """,
            params
        )

        if result.get('success', False):
            logging.debug(f"成功更新关键词 '{keyword}' 的数据: 分类={keyword_class}, 绝对搜索量={absolute_volume_series}")

            # 验证数据是否真的保存了
            verify_df = worker.execute_to_df(
                """SELECT keyword_interest_over_time_absolute_search_volume
                   FROM rising_keywords
                   WHERE task_id = ? AND keyword = ?""",
                [task_id, keyword]
            )

            if not verify_df.empty:
                saved_value = verify_df.iloc[0]['keyword_interest_over_time_absolute_search_volume']
                if saved_value:
                    logging.debug(f"验证成功: 关键词 '{keyword}' 的绝对搜索量已保存: {saved_value}")
                else:
                    logging.warning(f"验证失败: 关键词 '{keyword}' 的绝对搜索量未保存")
            else:
                logging.warning(f"验证失败: 未找到关键词 '{keyword}' 的记录")
        else:
            logging.error(f"更新关键词 '{keyword}' 的兴趣度数据失败: {result.get('error', '未知错误')}")
            print(f"更新关键词 '{keyword}' 的兴趣度数据失败: {result.get('error', '未知错误')}")
            
        return {
            "success": result.get('success', False),
            "error": result.get('error', None),
            "keyword": keyword,
            "reference_keyword": reference_keyword
        }
            
    except Exception as e:
        error_msg = f"更新关键词 '{keyword}' 的兴趣度数据时发生异常: {str(e)}"
        print(error_msg)
        return {
            "success": False,
            "error": str(e),
            "keyword": keyword,
            "reference_keyword": reference_keyword
        }

def check_keyword_exists_in_root(keyword):
    """检查关键词是否已存在于根关键词表中"""
    try:
        df = worker.execute_to_df("SELECT COUNT(*) as count FROM root_keywords WHERE keyword = ?", [keyword])
        return df.iloc[0]['count'] > 0
    except Exception as e:
        print(f"检查关键词 '{keyword}' 是否存在时出错: {str(e)}")
        return True  # 出错时返回True，避免重复插入

def add_keyword_to_root(keyword):
    """将关键词添加到根关键词表"""
    try:
        import time
        current_time = int(time.time())
        result = worker.execute(
            "INSERT INTO root_keywords (keyword, status, created_at, updated_at) VALUES (?, ?, ?, ?)",
            [keyword, 1, current_time, current_time]
        )
        return result.get('success', False)
    except Exception as e:
        print(f"添加关键词 '{keyword}' 到根关键词表时出错: {str(e)}")
        return False

def add_keyword_to_root_with_pool_info(keyword, parent_keyword=None):
    """将关键词添加到根关键词表，包含三池处理信息"""
    try:
        import time
        current_time = int(time.time())

        # 查找父关键词的ID
        parent_id = None
        if parent_keyword:
            parent_df = worker.execute_to_df(
                "SELECT id FROM root_keywords WHERE keyword = ?",
                [parent_keyword]
            )
            if not parent_df.empty:
                parent_id = parent_df.iloc[0]['id']

        result = worker.execute(
            """INSERT INTO root_keywords
               (keyword, status, created_at, updated_at, pool_status, parent_id, date_added)
               VALUES (?, ?, ?, ?, ?, ?, ?)""",
            [keyword, 1, current_time, current_time, 'newborn', parent_id, current_time]
        )
        return result.get('success', False)
    except Exception as e:
        print(f"添加关键词 '{keyword}' 到根关键词表(含池信息)时出错: {str(e)}")
        return False

def check_keyword_exists_in_rising(keyword, task_id=None, hours_window=24):
    """检查关键词是否在指定时间窗口内已存在于rising_keywords表中

    Args:
        keyword: 要检查的关键词
        task_id: 可选，指定任务ID进行检查
        hours_window: 时间窗口（小时），默认24小时

    Returns:
        bool: 关键词在时间窗口内是否已存在
    """
    try:
        # 计算时间窗口的起始时间（标准TIMESTAMP格式）
        from datetime import datetime, timedelta
        window_start = datetime.now() - timedelta(hours=hours_window)
        window_start_str = window_start.strftime('%Y-%m-%d %H:%M:%S')

        if task_id:
            # 检查特定任务中是否存在
            df = worker.execute_to_df(
                """SELECT COUNT(*) as count FROM rising_keywords
                   WHERE keyword = ? AND task_id = ?
                   AND created_at >= ?""",
                [keyword, task_id, window_start_str]
            )
        else:
            # 检查时间窗口内是否存在
            df = worker.execute_to_df(
                """SELECT COUNT(*) as count FROM rising_keywords
                   WHERE keyword = ?
                   AND created_at >= ?""",
                [keyword, window_start_str]
            )

        exists = df.iloc[0]['count'] > 0
        if exists:
            logging.debug(f"关键词 '{keyword}' 在过去{hours_window}小时内已存在")

        return exists
    except Exception as e:
        logging.error(f"检查关键词 '{keyword}' 是否存在时出错: {str(e)}")
        return True  # 出错时返回True，避免重复插入

def save_rising_df_to_db_with_dedup(rising_df, task_id, root_keyword, test_mode=False):
    """将上升查询数据保存到数据库，包含去重机制"""
    if test_mode:
        # 测试模式：跳过数据库保存，返回模拟结果
        success_count = len(rising_df)
        logging.info(f"测试模式：模拟保存 {success_count} 个关键词到数据库")
        return {
            "success": True,
            "inserted_count": success_count,
            "failed_count": 0,
            "duplicate_count": 0,
            "failed_keywords": [],
            "duplicate_keywords": []
        }

    success_count = 0
    failed_keywords = []
    duplicate_keywords = []

    for _, row in rising_df.iterrows():
        keyword = row['query']

        try:
            # 去重检查：24小时内的同一个新词，只插入一次
            if check_keyword_exists_in_rising(keyword, hours_window=24):
                duplicate_keywords.append(keyword)
                logging.debug(f"关键词 '{keyword}' 在24小时内已存在，跳过插入")
                continue

            params = [
                task_id,
                keyword,
                root_keyword,
                None,  # reference_keyword
                None,  # keyword_interest_over_time
                None   # reference_keyword_interest_over_time
            ]

            result = worker.execute(
                "INSERT INTO rising_keywords (task_id, keyword, root_keyword, reference_keyword, keyword_interest_over_time, reference_keyword_interest_over_time) "
                "VALUES (?, ?, ?, ?, ?, ?)",
                params
            )

            if result.get('success', False):
                success_count += 1
                logging.debug(f"成功插入关键词 '{keyword}'")
            else:
                failed_keywords.append(keyword)
                print(f"插入关键词 '{keyword}' 失败: {result.get('error', '未知错误')}")

        except Exception as e:
            failed_keywords.append(keyword)
            print(f"插入关键词 '{keyword}' 时发生异常: {str(e)}")

    return {
        "success": len(failed_keywords) == 0,
        "inserted_count": success_count,
        "failed_count": len(failed_keywords),
        "duplicate_count": len(duplicate_keywords),
        "failed_keywords": failed_keywords,
        "duplicate_keywords": duplicate_keywords
    }

def get_pending_keywords_for_batch_processing(task_id, batch_size=4, test_mode=False, test_keywords=None, test_processed_keywords=None):
    """获取待处理的关键词，用于批量处理

    Args:
        task_id: 任务ID
        batch_size: 批量大小，默认4个
        test_mode: 测试模式
        test_keywords: 测试关键词列表
        test_processed_keywords: 已处理的测试关键词集合

    Returns:
        list: 待处理的关键词列表，每个元素包含keyword和root_keyword
    """
    if test_mode and test_keywords:
        # 测试模式：返回未处理的测试关键词
        if test_processed_keywords is None:
            test_processed_keywords = set()

        result = []
        for keyword in test_keywords:
            if keyword not in test_processed_keywords and len(result) < batch_size:
                result.append({
                    'keyword': keyword,
                    'root_keyword': keyword  # 测试模式下使用自身作为根关键词
                })

        if result:
            logging.info(f"测试模式：返回 {len(result)} 个测试关键词进行批量处理")
        else:
            logging.info(f"测试模式：所有测试关键词已处理完成")
        return result
    try:
        df = worker.execute_to_df(
            """SELECT keyword, root_keyword
               FROM rising_keywords
               WHERE task_id = ?
               AND (keyword_interest_over_time IS NULL OR keyword_interest_over_time = '')
               LIMIT ?""",
            [task_id, batch_size]
        )

        result = df.to_dict(orient='records') if not df.empty else []
        logging.debug(f"get_pending_keywords_for_batch_processing: task_id={task_id}, batch_size={batch_size}, 返回{len(result)}条记录")

        # 验证返回的数据结构
        for i, item in enumerate(result):
            if not isinstance(item, dict):
                logging.error(f"数据库查询返回的第{i+1}项不是字典: {type(item)} - {item}")
            elif 'keyword' not in item or 'root_keyword' not in item:
                logging.error(f"数据库查询返回的第{i+1}项缺少字段: {item}")

        return result
    except Exception as e:
        print(f"获取待处理关键词时出错: {str(e)}")
        return []

def get_pool_statistics():
    """获取三池统计信息"""
    try:
        df = worker.execute_to_df(
            """SELECT pool_status, COUNT(*) as count
               FROM root_keywords
               WHERE pool_status IS NOT NULL
               GROUP BY pool_status"""
        )

        stats = {'newborn': 0, 'stable': 0, 'decayed': 0}
        for _, row in df.iterrows():
            stats[row['pool_status']] = row['count']

        return stats
    except Exception as e:
        print(f"获取池统计信息时出错: {str(e)}")
        return {'newborn': 0, 'stable': 0, 'decayed': 0}

def get_keywords_by_pool_status(pool_status, limit=None):
    """根据池状态获取关键词列表"""
    try:
        query = """SELECT id, keyword, pool_status, date_added, parent_id, created_at
                   FROM root_keywords
                   WHERE pool_status = ?
                   ORDER BY date_added DESC"""

        params = [pool_status]
        if limit:
            query += " LIMIT ?"
            params.append(limit)

        df = worker.execute_to_df(query, params)
        return df.to_dict(orient='records') if not df.empty else []
    except Exception as e:
        print(f"获取池状态 '{pool_status}' 的关键词时出错: {str(e)}")
        return []

def update_keyword_pool_status(keyword_id, new_status, reset_parent=False):
    """更新关键词的池状态"""
    try:
        import time
        current_time = int(time.time())

        if reset_parent:
            result = worker.execute(
                """UPDATE root_keywords
                   SET pool_status = ?, date_added = ?, parent_id = NULL
                   WHERE id = ?""",
                [new_status, current_time, keyword_id]
            )
        else:
            result = worker.execute(
                """UPDATE root_keywords
                   SET pool_status = ?, date_added = ?
                   WHERE id = ?""",
                [new_status, current_time, keyword_id]
            )

        return result.get('success', False)
    except Exception as e:
        print(f"更新关键词池状态时出错: {str(e)}")
        return False

def get_keyword_children(parent_id):
    """获取指定关键词的所有子关键词"""
    try:
        df = worker.execute_to_df(
            """SELECT id, keyword, pool_status
               FROM root_keywords
               WHERE parent_id = ?
               AND pool_status != 'decayed'""",
            [parent_id]
        )
        return df.to_dict(orient='records') if not df.empty else []
    except Exception as e:
        print(f"获取子关键词时出错: {str(e)}")
        return []

def save_rising_df_to_db(rising_df, task_id, root_keyword):
    """将上升查询数据保存到数据库"""
    # 为每行数据添加task_id和root_keyword
    success_count = 0
    failed_keywords = []

    for _, row in rising_df.iterrows():
        try:
            params = [
                task_id,
                row['query'],
                root_keyword,
                None,
                None,
                None
            ]

            result = worker.execute(
                "INSERT INTO rising_keywords (task_id, keyword, root_keyword, reference_keyword, keyword_interest_over_time, reference_keyword_interest_over_time) "
                "VALUES (?, ?, ?, ?, ?, ?)",
                params
            )

            if result.get('success', False):
                success_count += 1
            else:
                failed_keywords.append(row['query'])
                print(f"插入关键词 '{row['query']}' 失败: {result.get('error', '未知错误')}")

        except Exception as e:
            failed_keywords.append(row['query'])
            print(f"插入关键词 '{row['query']}' 时发生异常: {str(e)}")

    return {
        "success": len(failed_keywords) == 0,
        "inserted_count": success_count,
        "failed_count": len(failed_keywords),
        "failed_keywords": failed_keywords
    }