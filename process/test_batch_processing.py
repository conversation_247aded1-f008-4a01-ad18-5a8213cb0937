#!/usr/bin/env python3
"""
批量处理和四层筛选测试脚本

测试指定task_id的rising keywords数据：
1. 批量获取兴趣度数据
2. 四层筛选模型验证
3. 绝对搜索量计算
4. 数据库更新

使用方法：
python test_batch_processing.py --task-id 20250722_10 --batch-size 4
"""

import logging
import argparse
import sys
from datetime import datetime
from db_ops import get_pending_keywords_for_batch_processing, get_config_value, worker
from analysis import process_keywords_batch, calculate_keyword_class, calculate_absolute_search_volume_series
from trends_api import get_interest_over_time_batch
from utils import setup_logger

def setup_test_logger():
    """设置测试日志"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f"test_batch_{timestamp}"
    logger = setup_logger(log_filename)
    
    # 设置详细日志级别
    logging.getLogger().setLevel(logging.DEBUG)
    
    return logger

def test_get_pending_keywords(task_id, batch_size):
    """测试获取待处理关键词"""
    print(f"\n=== 测试获取待处理关键词 ===")
    print(f"Task ID: {task_id}")
    print(f"Batch Size: {batch_size}")
    
    keywords_batch = get_pending_keywords_for_batch_processing(task_id, batch_size)
    
    print(f"找到 {len(keywords_batch)} 个待处理关键词:")
    for i, item in enumerate(keywords_batch, 1):
        print(f"  {i}. {item['keyword']} (来源: {item['root_keyword']})")
    
    return keywords_batch

def test_batch_interest_data(keywords_batch, reference_keyword):
    """测试批量获取兴趣度数据"""
    print(f"\n=== 测试批量获取兴趣度数据 ===")
    print(f"参考关键词: {reference_keyword}")
    
    if not keywords_batch:
        print("没有关键词需要处理")
        return None
    
    keywords_list = [item['keyword'] for item in keywords_batch]
    print(f"批量查询关键词: {keywords_list}")
    
    try:
        batch_data = get_interest_over_time_batch(keywords_list, reference_keyword)
        
        print(f"成功获取 {len(batch_data)} 个关键词的数据:")
        for keyword, data in batch_data.items():
            if isinstance(data, list) and len(data) >= 7:
                peak = max(data)
                latest = data[-1]
                print(f"  {keyword}: 峰值={peak}, 最新值={latest}, 数据={data}")
            else:
                print(f"  {keyword}: 数据异常 - {data}")
        
        return batch_data
        
    except Exception as e:
        print(f"批量获取数据失败: {str(e)}")
        return None

def test_four_layer_filtering(keyword_data, reference_data, keyword_name):
    """测试四层筛选模型（详细日志）"""
    print(f"\n=== 四层筛选测试: {keyword_name} ===")
    
    # 将数据转换为数值列表
    keyword_data_list = [int(round(float(x))) for x in keyword_data]
    reference_data_list = [int(round(float(x))) for x in reference_data]
    
    print(f"关键词数据: {keyword_data_list}")
    print(f"基准词数据: {reference_data_list}")
    
    # 确保数据长度为7天
    if len(keyword_data_list) < 7 or len(reference_data_list) < 7:
        print(f"❌ 数据长度不足7天: keyword={len(keyword_data_list)}, reference={len(reference_data_list)}")
        return None
    
    # 第一层：基线标准
    print(f"\n--- 第一层：基线标准 ---")
    keyword_first_3_avg = sum(keyword_data_list[:3]) / 3
    reference_first_3_avg = sum(reference_data_list[:3]) / 3
    
    print(f"关键词前3天平均值: {keyword_first_3_avg:.2f}")
    print(f"基准词前3天平均值: {reference_first_3_avg:.2f}")
    
    if reference_first_3_avg == 0:
        print("❌ 基准词前3天平均值为0，无法计算相对基线")
        return None
    
    baseline_ratio = keyword_first_3_avg / reference_first_3_avg
    print(f"基线比例: {baseline_ratio:.4f}")
    print(f"基线标准: {baseline_ratio:.4f} < 0.05 ? {'✅ 通过' if baseline_ratio < 0.05 else '❌ 未通过'}")
    
    if baseline_ratio >= 0.05:
        print(f"❌ 未通过基线标准")
        return None
    
    # 第二层：增长标准
    print(f"\n--- 第二层：增长标准 ---")
    keyword_peak = max(keyword_data_list)
    keyword_baseline = keyword_first_3_avg
    
    if keyword_baseline == 0:
        non_zero_values = [x for x in keyword_data_list[:3] if x > 0]
        if not non_zero_values:
            keyword_baseline = 0.1
        else:
            keyword_baseline = min(non_zero_values)
        print(f"调整后的基线值: {keyword_baseline}")
    
    growth_ratio = keyword_peak / keyword_baseline
    print(f"关键词峰值: {keyword_peak}")
    print(f"关键词基线值: {keyword_baseline:.2f}")
    print(f"增长比例: {growth_ratio:.2f}")
    print(f"增长标准: {growth_ratio:.2f} > 10 ? {'✅ 通过' if growth_ratio > 10 else '❌ 未通过'}")
    
    if growth_ratio <= 10:
        print(f"❌ 未通过增长标准")
        return None
    
    # 第三层：时效性标准
    print(f"\n--- 第三层：时效性标准 ---")
    day_7_value = keyword_data_list[6]
    peak_90_percent = keyword_peak * 0.9
    
    is_peak_on_day_7 = (day_7_value == keyword_peak)
    is_day_7_near_peak = (day_7_value >= peak_90_percent)
    
    print(f"第7天数值: {day_7_value}")
    print(f"峰值: {keyword_peak}")
    print(f"峰值90%: {peak_90_percent:.1f}")
    print(f"峰值在第7天: {is_peak_on_day_7}")
    print(f"第7天≥峰值90%: {is_day_7_near_peak}")
    print(f"时效性标准: {'✅ 通过' if (is_peak_on_day_7 or is_day_7_near_peak) else '❌ 未通过'}")
    
    if not (is_peak_on_day_7 or is_day_7_near_peak):
        print(f"❌ 未通过时效性标准")
        return None
    
    # 第四层：超越标准
    print(f"\n--- 第四层：超越标准 ---")
    reference_day_7 = reference_data_list[6]
    print(f"关键词第7天: {day_7_value}")
    print(f"基准词第7天: {reference_day_7}")
    print(f"超越标准: {day_7_value} > {reference_day_7} ? {'✅ 通过' if day_7_value > reference_day_7 else '❌ 未通过'}")
    
    if day_7_value <= reference_day_7:
        print(f"❌ 未通过超越标准")
        return None
    
    print(f"\n🎉 {keyword_name} 通过所有四层筛选！")
    return 'A'

def test_absolute_volume_calculation(keyword_data, reference_data, reference_keyword):
    """测试绝对搜索量计算"""
    print(f"\n=== 测试绝对搜索量计算 ===")
    
    absolute_series = calculate_absolute_search_volume_series(
        keyword_data, reference_data, reference_keyword
    )
    
    print(f"绝对搜索量序列: {absolute_series}")
    
    # 解析并显示详细信息
    absolute_values = [int(x) for x in absolute_series.split(',')]
    peak_absolute = max(absolute_values)
    avg_absolute = sum(absolute_values) / len(absolute_values)
    
    print(f"峰值绝对搜索量: {peak_absolute}k")
    print(f"平均绝对搜索量: {avg_absolute:.1f}k")
    
    return absolute_series

def test_database_update(task_id, keyword, reference_keyword, keyword_class, absolute_series):
    """测试数据库更新"""
    print(f"\n=== 测试数据库更新 ===")
    print(f"关键词: {keyword}")
    print(f"分类: {keyword_class}")
    print(f"绝对搜索量: {absolute_series}")
    
    # 这里只是模拟，不实际更新数据库
    print("✅ 数据库更新测试完成（模拟）")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='批量处理和四层筛选测试脚本')
    parser.add_argument('--task-id', required=True, help='任务ID')
    parser.add_argument('--batch-size', type=int, default=4, help='批量大小')
    parser.add_argument('--reference-keyword', default='animal generator', help='参考关键词')
    
    args = parser.parse_args()
    
    # 设置日志
    logger = setup_test_logger()
    logger.info(f"开始批量处理测试，Task ID: {args.task_id}")
    
    try:
        # 1. 测试获取待处理关键词
        keywords_batch = test_get_pending_keywords(args.task_id, args.batch_size)
        
        if not keywords_batch:
            print("没有找到待处理的关键词")
            return
        
        # 2. 测试批量获取兴趣度数据
        batch_data = test_batch_interest_data(keywords_batch, args.reference_keyword)
        
        if not batch_data:
            print("无法获取兴趣度数据")
            return
        
        # 3. 对每个关键词进行详细测试
        for item in keywords_batch:
            keyword = item['keyword']
            root_keyword = item['root_keyword']
            
            print(f"\n{'='*60}")
            print(f"测试关键词: {keyword} (来源: {root_keyword})")
            print(f"{'='*60}")
            
            keyword_data = batch_data.get(keyword)
            reference_data = batch_data.get(args.reference_keyword)
            
            if not keyword_data or not reference_data:
                print(f"❌ 关键词 '{keyword}' 数据不完整")
                continue
            
            # 四层筛选测试
            keyword_class = test_four_layer_filtering(keyword_data, reference_data, keyword)
            
            # 绝对搜索量计算测试
            absolute_series = test_absolute_volume_calculation(
                keyword_data, reference_data, args.reference_keyword
            )
            
            # 数据库更新测试
            test_database_update(args.task_id, keyword, args.reference_keyword, keyword_class, absolute_series)
        
        print(f"\n{'='*60}")
        print("测试完成！")
        print(f"{'='*60}")
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        sys.exit(1)

if __name__ == "__main__":
    main()
