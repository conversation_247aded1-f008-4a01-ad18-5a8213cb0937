#!/usr/bin/env python3
"""
测试结果验证脚本

详细验证main_test.py的执行结果，确保所有功能正常工作

使用方法：
python validate_test_results.py --task-id <task_id>
"""

import argparse
import sys
from datetime import datetime
from db_ops import worker

def validate_rising_keywords_data(task_id):
    """验证rising_keywords表的数据完整性"""
    print(f"\n{'='*60}")
    print(f"1. Rising Keywords 数据验证")
    print(f"{'='*60}")
    
    try:
        # 获取基本统计
        stats_df = worker.execute_to_df(
            """SELECT 
                COUNT(*) as total_count,
                COUNT(keyword_interest_over_time) as has_interest_data,
                COUNT(keyword_interest_over_time_absolute_search_volume) as has_absolute_data,
                COUNT(keyword_class) as has_classification
               FROM rising_keywords 
               WHERE task_id = ?""",
            [task_id]
        )
        
        if stats_df.empty:
            print(f"❌ 未找到任务 {task_id} 的数据")
            return False
        
        stats = stats_df.iloc[0]
        print(f"📊 基本统计:")
        print(f"   总记录数: {stats['total_count']}")
        print(f"   有兴趣度数据: {stats['has_interest_data']}")
        print(f"   有绝对搜索量数据: {stats['has_absolute_data']}")
        print(f"   有分类数据: {stats['has_classification']}")
        
        # 验证分类分布
        class_df = worker.execute_to_df(
            """SELECT keyword_class, COUNT(*) as count 
               FROM rising_keywords 
               WHERE task_id = ? 
               GROUP BY keyword_class""",
            [task_id]
        )
        
        print(f"\n📈 分类分布:")
        total_classified = 0
        a_grade_count = 0
        
        for _, row in class_df.iterrows():
            keyword_class = row['keyword_class'] or '未分类'
            count = row['count']
            print(f"   {keyword_class}: {count} 个")
            
            if keyword_class != '未分类':
                total_classified += count
            if keyword_class == 'A':
                a_grade_count = count
        
        print(f"   总分类数: {total_classified}")
        
        # 验证绝对搜索量数据格式
        print(f"\n🔢 绝对搜索量数据验证:")
        volume_df = worker.execute_to_df(
            """SELECT keyword, keyword_interest_over_time_absolute_search_volume
               FROM rising_keywords 
               WHERE task_id = ? 
               AND keyword_interest_over_time_absolute_search_volume IS NOT NULL
               LIMIT 5""",
            [task_id]
        )
        
        if not volume_df.empty:
            print(f"✅ 找到 {len(volume_df)} 个样本数据:")
            for _, row in volume_df.iterrows():
                keyword = row['keyword']
                volume_series = row['keyword_interest_over_time_absolute_search_volume']
                
                try:
                    values = [int(x) for x in volume_series.split(',')]
                    peak = max(values)
                    avg = sum(values) / len(values)
                    print(f"   {keyword}: 长度={len(values)}, 峰值={peak}k, 平均={avg:.1f}k")
                except Exception as e:
                    print(f"   {keyword}: 数据格式错误 - {e}")
        else:
            print(f"❌ 未找到绝对搜索量数据")
        
        return True  # 数据验证通过，不依赖A级关键词数量
        
    except Exception as e:
        print(f"❌ 验证失败: {str(e)}")
        return False

def validate_pool_integration(task_id):
    """验证三池集成功能"""
    print(f"\n{'='*60}")
    print(f"2. 三池集成验证")
    print(f"{'='*60}")
    
    try:
        # 获取A级关键词
        a_keywords_df = worker.execute_to_df(
            """SELECT keyword FROM rising_keywords 
               WHERE task_id = ? AND keyword_class = 'A'""",
            [task_id]
        )
        
        if a_keywords_df.empty:
            print(f"ℹ️ 本次测试未发现A级关键词，跳过池集成验证")
            return True

        a_keywords = a_keywords_df['keyword'].tolist()
        print(f"🎯 发现 {len(a_keywords)} 个A级关键词: {a_keywords}")
        
        # 验证是否已加入新生池
        pool_success = 0
        for keyword in a_keywords:
            pool_df = worker.execute_to_df(
                """SELECT pool_status, date_added, parent_id 
                   FROM root_keywords 
                   WHERE keyword = ?""",
                [keyword]
            )
            
            if not pool_df.empty:
                pool_info = pool_df.iloc[0]
                pool_status = pool_info['pool_status']
                date_added = pool_info['date_added']
                parent_id = pool_info['parent_id']
                
                print(f"   ✅ {keyword}: 池状态={pool_status}, 时间={date_added}, 父ID={parent_id or 'None'}")
                
                if pool_status == 'newborn':
                    pool_success += 1
            else:
                print(f"   ❌ {keyword}: 未找到池记录")
        
        success_rate = pool_success / len(a_keywords) * 100
        print(f"\n📊 池集成成功率: {pool_success}/{len(a_keywords)} ({success_rate:.1f}%)")
        
        return success_rate >= 80  # 80%以上成功率认为正常
        
    except Exception as e:
        print(f"❌ 池集成验证失败: {str(e)}")
        return False

def validate_four_layer_filtering(task_id):
    """验证四层筛选模型"""
    print(f"\n{'='*60}")
    print(f"3. 四层筛选模型验证")
    print(f"{'='*60}")
    
    try:
        # 获取有完整数据的关键词样本
        sample_df = worker.execute_to_df(
            """SELECT keyword, keyword_interest_over_time, 
                      reference_keyword_interest_over_time, keyword_class
               FROM rising_keywords 
               WHERE task_id = ? 
               AND keyword_interest_over_time IS NOT NULL 
               AND reference_keyword_interest_over_time IS NOT NULL
               LIMIT 10""",
            [task_id]
        )
        
        if sample_df.empty:
            print(f"❌ 未找到完整的数据样本")
            return False
        
        print(f"📊 分析 {len(sample_df)} 个数据样本:")
        
        correct_classifications = 0
        total_samples = len(sample_df)
        
        for _, row in sample_df.iterrows():
            keyword = row['keyword']
            keyword_data_str = row['keyword_interest_over_time']
            reference_data_str = row['reference_keyword_interest_over_time']
            stored_class = row['keyword_class']
            
            try:
                # 重新计算分类
                keyword_data = [int(x) for x in keyword_data_str.split(',')]
                reference_data = [int(x) for x in reference_data_str.split(',')]
                
                # 简化的四层筛选验证
                if len(keyword_data) >= 7 and len(reference_data) >= 7:
                    # 基线标准
                    keyword_first_3_avg = sum(keyword_data[:3]) / 3
                    reference_first_3_avg = sum(reference_data[:3]) / 3
                    baseline_ratio = keyword_first_3_avg / reference_first_3_avg if reference_first_3_avg > 0 else 999
                    
                    # 增长标准
                    keyword_peak = max(keyword_data)
                    keyword_baseline = max(keyword_first_3_avg, 0.1)
                    growth_ratio = keyword_peak / keyword_baseline
                    
                    # 时效性标准
                    day_7_value = keyword_data[6]
                    peak_90_percent = keyword_peak * 0.9
                    time_valid = (day_7_value == keyword_peak) or (day_7_value >= peak_90_percent)
                    
                    # 超越标准
                    reference_day_7 = reference_data[6]
                    exceed_valid = day_7_value > reference_day_7
                    
                    # 判断是否应该为A级
                    should_be_a = (baseline_ratio < 0.05 and growth_ratio > 10 and 
                                  time_valid and exceed_valid)
                    
                    expected_class = 'A' if should_be_a else None
                    is_correct = (stored_class == expected_class)
                    
                    if is_correct:
                        correct_classifications += 1
                    
                    status = "✅" if is_correct else "❌"
                    print(f"   {status} {keyword}: 存储={stored_class or '未分类'}, "
                          f"预期={'A' if should_be_a else '未分类'}")
                    
                    if not is_correct:
                        print(f"      详情: 基线={baseline_ratio:.4f}, 增长={growth_ratio:.1f}, "
                              f"时效={time_valid}, 超越={exceed_valid}")
                
            except Exception as e:
                print(f"   ❌ {keyword}: 数据解析错误 - {e}")
        
        accuracy = correct_classifications / total_samples * 100
        print(f"\n📊 四层筛选准确率: {correct_classifications}/{total_samples} ({accuracy:.1f}%)")
        
        return accuracy >= 90  # 90%以上准确率认为正常
        
    except Exception as e:
        print(f"❌ 四层筛选验证失败: {str(e)}")
        return False

def validate_batch_processing(task_id):
    """验证批量处理效果"""
    print(f"\n{'='*60}")
    print(f"4. 批量处理效果验证")
    print(f"{'='*60}")
    
    try:
        # 检查数据完整性
        completeness_df = worker.execute_to_df(
            """SELECT 
                COUNT(*) as total,
                COUNT(keyword_interest_over_time) as processed,
                COUNT(CASE WHEN keyword_interest_over_time IS NULL THEN 1 END) as pending
               FROM rising_keywords 
               WHERE task_id = ?""",
            [task_id]
        )
        
        if completeness_df.empty:
            print(f"❌ 未找到数据")
            return False
        
        stats = completeness_df.iloc[0]
        total = stats['total']
        processed = stats['processed']
        pending = stats['pending']
        
        completion_rate = processed / total * 100 if total > 0 else 0
        
        print(f"📊 处理完成度:")
        print(f"   总关键词数: {total}")
        print(f"   已处理: {processed}")
        print(f"   待处理: {pending}")
        print(f"   完成率: {completion_rate:.1f}%")
        
        # 检查错误处理
        error_df = worker.execute_to_df(
            """SELECT keyword FROM rising_keywords 
               WHERE task_id = ? 
               AND keyword_interest_over_time IS NULL 
               LIMIT 5""",
            [task_id]
        )
        
        if not error_df.empty:
            print(f"\n⚠️ 未处理的关键词样本:")
            for _, row in error_df.iterrows():
                print(f"   - {row['keyword']}")
        
        return completion_rate >= 80  # 80%以上完成率认为正常
        
    except Exception as e:
        print(f"❌ 批量处理验证失败: {str(e)}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='测试结果验证脚本')
    parser.add_argument('--task-id', required=True, help='要验证的任务ID')
    
    args = parser.parse_args()
    
    print(f"{'='*60}")
    print(f"Main Test 结果验证")
    print(f"任务ID: {args.task_id}")
    print(f"验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"{'='*60}")
    
    # 执行各项验证
    results = {
        'rising_keywords': validate_rising_keywords_data(args.task_id),
        'pool_integration': validate_pool_integration(args.task_id),
        'four_layer_filtering': validate_four_layer_filtering(args.task_id),
        'batch_processing': validate_batch_processing(args.task_id)
    }
    
    # 总结报告
    print(f"\n{'='*60}")
    print(f"验证总结")
    print(f"{'='*60}")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name.replace('_', ' ').title()}: {status}")
        if result:
            passed += 1
    
    success_rate = passed / total * 100
    print(f"\n📊 总体通过率: {passed}/{total} ({success_rate:.1f}%)")
    
    if success_rate >= 75:
        print(f"🎉 测试基本通过！Main.py 核心功能正常工作。")
        sys.exit(0)
    else:
        print(f"⚠️ 测试未完全通过，建议检查失败项目。")
        sys.exit(1)

if __name__ == "__main__":
    main()
