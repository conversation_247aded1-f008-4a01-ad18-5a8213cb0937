#!/usr/bin/env python3
"""
流量计算修复验证脚本

验证修复后的绝对搜索量计算是否正确
"""

from trends_api import get_interest_over_time_by_word
from analysis import calculate_absolute_search_volume_series
import logging

# 设置日志
logging.basicConfig(level=logging.DEBUG)
logging.getLogger('urllib3.connectionpool').setLevel(logging.WARNING)

def test_flow_calculation():
    """测试流量计算修复"""
    reference_keyword = "animal generator"
    test_keyword = "anime final strike codes"
    
    print(f"=== 流量计算修复验证 ===")
    print(f"测试关键词: {test_keyword}")
    print(f"基准词: {reference_keyword}")
    print(f"基准词参考流量: 4k")
    
    try:
        # 获取数据
        interest_data = get_interest_over_time_by_word(test_keyword, reference_keyword)
        
        keyword_data = interest_data[0]
        reference_data = interest_data[1]
        
        print(f"\n=== 原始数据 ===")
        print(f"关键词数据: {keyword_data}")
        print(f"基准词数据: {reference_data}")
        
        # 计算绝对搜索量
        absolute_series = calculate_absolute_search_volume_series(
            keyword_data, reference_data, reference_keyword
        )
        
        print(f"\n=== 绝对搜索量计算 ===")
        print(f"绝对搜索量序列: {absolute_series}")
        
        # 详细分析
        absolute_values = [int(float(x)) for x in absolute_series.split(',')]
        keyword_values = [int(round(float(x))) for x in keyword_data]
        reference_values = [int(round(float(x))) for x in reference_data]
        
        print(f"\n=== 详细计算验证 ===")
        print(f"{'天数':<4} {'关键词':<8} {'基准词':<8} {'比例':<8} {'绝对值(k)':<10} {'验算':<10}")
        print(f"{'-'*55}")
        
        for i in range(len(keyword_values)):
            keyword_val = keyword_values[i]
            reference_val = reference_values[i]
            absolute_val = absolute_values[i]
            
            # 验算：(关键词值 / 基准词值) * 4k
            if reference_val > 0:
                ratio = keyword_val / reference_val
                expected = int(ratio * 4)
            else:
                ratio = 0
                expected = 0
            
            match = "✅" if expected == absolute_val else "❌"
            
            print(f"第{i+1}天  {keyword_val:<8} {reference_val:<8} {ratio:<8.2f} {absolute_val:<10} {expected:<10} {match}")
        
        # 统计信息
        peak_absolute = max(absolute_values)
        peak_keyword = max(keyword_values)
        peak_reference = max(reference_values)
        
        print(f"\n=== 统计信息 ===")
        print(f"关键词峰值: {peak_keyword}")
        print(f"基准词峰值: {peak_reference}")
        print(f"绝对搜索量峰值: {peak_absolute}k")
        
        # 验证峰值计算
        if peak_reference > 0:
            expected_peak = int((peak_keyword / peak_reference) * 4)
            print(f"预期峰值: {expected_peak}k")
            print(f"峰值计算: {'✅ 正确' if expected_peak == peak_absolute else '❌ 错误'}")
        
        # 判断是否合理
        if peak_absolute > 0:
            print(f"\n🎉 流量计算修复成功！绝对搜索量不再为0")
            
            # 检查是否符合爆发性增长
            baseline_avg = sum(keyword_values[:3]) / 3
            if baseline_avg < peak_keyword * 0.1:  # 基线很低，峰值很高
                print(f"💡 该关键词显示爆发性增长模式：基线={baseline_avg:.1f}, 峰值={peak_keyword}")
            else:
                print(f"💡 该关键词增长模式：基线={baseline_avg:.1f}, 峰值={peak_keyword}")
        else:
            print(f"\n❌ 流量计算仍有问题，绝对搜索量为0")
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    test_flow_calculation()
