import logging
import time
from db_ops import worker, get_reference_word

class PoolManager:
    """三池处理管理器
    
    负责管理关键词在新生池、稳定池、衰退池之间的流转
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def validate_keyword_data(self, keyword):
        """验证关键词是否有足够的数据进行评估

        Args:
            keyword: 关键词

        Returns:
            bool: 是否有足够的数据
        """
        try:
            # 检查是否在 rising_keywords 表中有数据
            df = worker.execute_to_df(
                """SELECT COUNT(*) as count
                   FROM rising_keywords
                   WHERE keyword = ?
                   AND keyword_interest_over_time IS NOT NULL
                   AND keyword_interest_over_time != ''""",
                [keyword]
            )

            return df.iloc[0]['count'] > 0 if not df.empty else False

        except Exception as e:
            self.logger.error(f"验证关键词 '{keyword}' 数据时出错: {str(e)}")
            return False
    
    def get_reference_traffic(self, reference_keyword="animal generator"):
        """获取基准词的参考流量"""
        try:
            reference_df = get_reference_word()
            for _, row in reference_df.iterrows():
                if row['keyword'] == reference_keyword:
                    return row.get('daily_search_volume', 4)
            return 4  # 默认值
        except Exception as e:
            self.logger.error(f"获取基准词参考流量时出错: {str(e)}")
            return 4
    
    def get_newborn_pool_keywords(self):
        """获取新生池中的关键词"""
        try:
            df = worker.execute_to_df(
                """SELECT id, keyword, date_added, parent_id
                   FROM root_keywords
                   WHERE pool_status = 'newborn'"""
            )

            # 过滤掉 date_added 为 None 的记录
            if not df.empty:
                df = df[df['date_added'].notna()]

            return df.to_dict(orient='records') if not df.empty else []
        except Exception as e:
            self.logger.error(f"获取新生池关键词时出错: {str(e)}")
            return []
    
    def get_stable_pool_keywords(self):
        """获取稳定池中的关键词"""
        try:
            df = worker.execute_to_df(
                """SELECT id, keyword, date_added 
                   FROM root_keywords 
                   WHERE pool_status = 'stable'"""
            )
            return df.to_dict(orient='records') if not df.empty else []
        except Exception as e:
            self.logger.error(f"获取稳定池关键词时出错: {str(e)}")
            return []
    
    def get_keyword_performance_data(self, keyword):
        """获取关键词的表现数据

        Args:
            keyword: 关键词

        Returns:
            dict: 包含关键词表现数据的字典
        """
        try:
            # 获取关键词的所有历史数据以计算历史峰值
            df = worker.execute_to_df(
                """SELECT keyword_interest_over_time, keyword_interest_over_time_absolute_search_volume, created_at
                   FROM rising_keywords
                   WHERE keyword = ?
                   AND keyword_interest_over_time IS NOT NULL
                   ORDER BY created_at DESC""",
                [keyword]
            )

            if df.empty:
                self.logger.warning(f"关键词 '{keyword}' 在 rising_keywords 表中没有数据记录")
                return None

            # 收集所有历史数据
            all_interest_data = []
            all_absolute_data = []

            for _, row in df.iterrows():
                interest_data_str = row['keyword_interest_over_time']
                absolute_data_str = row['keyword_interest_over_time_absolute_search_volume']

                if interest_data_str:
                    interest_data = [int(float(x)) for x in interest_data_str.split(',')]
                    all_interest_data.extend(interest_data)

                if absolute_data_str:
                    absolute_data = [int(float(x)) for x in absolute_data_str.split(',')]
                    all_absolute_data.extend(absolute_data)

            if not all_interest_data:
                self.logger.warning(f"关键词 '{keyword}' 没有有效的兴趣度数据")
                return None

            # 获取最新的数据（第一行）
            latest_row = df.iloc[0]
            latest_interest_str = latest_row['keyword_interest_over_time']
            latest_absolute_str = latest_row['keyword_interest_over_time_absolute_search_volume']

            latest_interest_data = [int(float(x)) for x in latest_interest_str.split(',')] if latest_interest_str else []
            latest_absolute_data = [int(float(x)) for x in latest_absolute_str.split(',')] if latest_absolute_str else []

            return {
                'latest_interest_data': latest_interest_data,
                'latest_absolute_data': latest_absolute_data,
                'historical_peak_value': max(all_interest_data),  # 历史峰值（相对值）
                'historical_peak_absolute': max(all_absolute_data) if all_absolute_data else 0,  # 历史峰值（绝对值）
                'latest_value': latest_interest_data[-1] if latest_interest_data else 0,
                'average_last_3': sum(latest_interest_data[-3:]) / 3 if len(latest_interest_data) >= 3 else 0,
                'average_last_3_absolute': sum(latest_absolute_data[-3:]) / 3 if len(latest_absolute_data) >= 3 else 0
            }

        except Exception as e:
            self.logger.error(f"获取关键词 '{keyword}' 表现数据时出错: {str(e)}")
            return None
    
    def promote_to_stable_pool(self, keyword_id, keyword):
        """将关键词晋升到稳定池"""
        try:
            current_time = int(time.time())
            result = worker.execute(
                """UPDATE root_keywords 
                   SET pool_status = 'stable', 
                       date_added = ?, 
                       parent_id = NULL 
                   WHERE id = ?""",
                [current_time, keyword_id]
            )
            
            if result.get('success', False):
                self.logger.info(f"关键词 '{keyword}' 已晋升到稳定池")
                return True
            else:
                self.logger.error(f"晋升关键词 '{keyword}' 到稳定池失败: {result.get('error')}")
                return False
                
        except Exception as e:
            self.logger.error(f"晋升关键词 '{keyword}' 到稳定池时出错: {str(e)}")
            return False
    
    def demote_to_decay_pool(self, keyword_id, keyword):
        """将关键词降级到衰退池"""
        try:
            current_time = int(time.time())
            result = worker.execute(
                """UPDATE root_keywords 
                   SET pool_status = 'decayed', 
                       date_added = ? 
                   WHERE id = ?""",
                [current_time, keyword_id]
            )
            
            if result.get('success', False):
                self.logger.info(f"关键词 '{keyword}' 已降级到衰退池")
                return True
            else:
                self.logger.error(f"降级关键词 '{keyword}' 到衰退池失败: {result.get('error')}")
                return False
                
        except Exception as e:
            self.logger.error(f"降级关键词 '{keyword}' 到衰退池时出错: {str(e)}")
            return False
    
    def evaluate_newborn_keyword(self, keyword_data):
        """评估新生池关键词是否应该晋升或降级
        
        Args:
            keyword_data: 关键词数据字典
            
        Returns:
            str: 'promote', 'demote', 'keep'
        """
        keyword_id = keyword_data['id']
        keyword = keyword_data['keyword']
        date_added = keyword_data['date_added']
        
        # 检查是否已在新生池3天
        current_time = int(time.time())
        days_in_pool = (current_time - date_added) / (24 * 3600)
        
        if days_in_pool < 3:
            return 'keep'  # 还未满3天，继续观察

        # 验证关键词是否有足够的数据
        if not self.validate_keyword_data(keyword):
            self.logger.warning(f"关键词 '{keyword}' 缺少必要的数据，跳过评估")
            return 'keep'

        # 获取关键词表现数据
        performance = self.get_keyword_performance_data(keyword)
        if not performance:
            self.logger.warning(f"无法获取关键词 '{keyword}' 的表现数据，可能是网络问题或数据缺失，保持观察")
            return 'keep'
        
        # 获取基准词参考流量
        reference_traffic = self.get_reference_traffic()
        
        # 判断是否稳定：平均绝对搜索量是否持续高于基准词的10倍
        stable_threshold = reference_traffic * 2  # 8k

        if performance['average_last_3_absolute'] >= stable_threshold:
            self.logger.info(f"关键词 '{keyword}' 表现稳定 (平均绝对搜索量: {performance['average_last_3_absolute']}k >= {stable_threshold}k)，晋升到稳定池")
            return 'promote'
        else:
            self.logger.info(f"关键词 '{keyword}' 表现不佳 (平均绝对搜索量: {performance['average_last_3_absolute']}k < {stable_threshold}k)，降级到衰退池")
            return 'demote'
    
    def evaluate_stable_keyword(self, keyword_data):
        """评估稳定池关键词是否应该降级
        
        Args:
            keyword_data: 关键词数据字典
            
        Returns:
            str: 'demote', 'keep'
        """
        keyword = keyword_data['keyword']
        
        # 获取关键词表现数据
        performance = self.get_keyword_performance_data(keyword)
        if not performance:
            self.logger.warning(f"无法获取关键词 '{keyword}' 的表现数据，可能是网络问题或数据缺失，保持观察")
            return 'keep'
        
        # 获取基准词参考流量
        reference_traffic = self.get_reference_traffic()
        stable_threshold = reference_traffic * 3  # 12k
        
        # 检查3日均值和近期健康度
        if performance['average_last_3_absolute'] < stable_threshold:
            self.logger.info(f"关键词 '{keyword}' 热度下降 (平均绝对搜索量: {performance['average_last_3_absolute']}k < {stable_threshold}k)，降级到衰退池")
            return 'demote'
        
        return 'keep'
    
    def process_newborn_pool(self):
        """处理新生池中的关键词"""
        self.logger.info("开始处理新生池关键词")
        
        newborn_keywords = self.get_newborn_pool_keywords()
        if not newborn_keywords:
            self.logger.info("新生池中没有关键词需要处理")
            return
        
        stats = {'promoted': 0, 'demoted': 0, 'kept': 0}
        
        for keyword_data in newborn_keywords:
            keyword_id = keyword_data['id']
            keyword = keyword_data['keyword']
            
            try:
                action = self.evaluate_newborn_keyword(keyword_data)
                
                if action == 'promote':
                    if self.promote_to_stable_pool(keyword_id, keyword):
                        stats['promoted'] += 1
                elif action == 'demote':
                    if self.demote_to_decay_pool(keyword_id, keyword):
                        stats['demoted'] += 1
                        # 触发连坐机制
                        self.trigger_cascade_demotion(keyword_id)
                else:
                    stats['kept'] += 1
                    
            except Exception as e:
                self.logger.error(f"处理新生池关键词 '{keyword}' 时出错: {str(e)}")
        
        self.logger.info(f"新生池处理完成: 晋升{stats['promoted']}个，降级{stats['demoted']}个，保持{stats['kept']}个")
    
    def process_stable_pool(self):
        """处理稳定池中的关键词"""
        self.logger.info("开始处理稳定池关键词")
        
        stable_keywords = self.get_stable_pool_keywords()
        if not stable_keywords:
            self.logger.info("稳定池中没有关键词需要处理")
            return
        
        stats = {'demoted': 0, 'kept': 0}
        
        for keyword_data in stable_keywords:
            keyword_id = keyword_data['id']
            keyword = keyword_data['keyword']
            
            try:
                action = self.evaluate_stable_keyword(keyword_data)
                
                if action == 'demote':
                    if self.demote_to_decay_pool(keyword_id, keyword):
                        stats['demoted'] += 1
                else:
                    stats['kept'] += 1
                    
            except Exception as e:
                self.logger.error(f"处理稳定池关键词 '{keyword}' 时出错: {str(e)}")
        
        self.logger.info(f"稳定池处理完成: 降级{stats['demoted']}个，保持{stats['kept']}个")
    
    def trigger_cascade_demotion(self, parent_keyword_id):
        """触发连坐降级机制
        
        Args:
            parent_keyword_id: 被降级的父关键词ID
        """
        try:
            # 查找所有子关键词
            children_df = worker.execute_to_df(
                """SELECT id, keyword 
                   FROM root_keywords 
                   WHERE parent_id = ? 
                   AND pool_status != 'decayed'""",
                [parent_keyword_id]
            )
            
            if children_df.empty:
                return
            
            self.logger.info(f"触发连坐机制，找到{len(children_df)}个子关键词")
            
            for _, child in children_df.iterrows():
                child_id = child['id']
                child_keyword = child['keyword']
                
                # 进行独立生存测试
                if self.independent_survival_test(child_id, child_keyword, parent_keyword_id):
                    # 通过测试，独立门户
                    self.make_independent(child_id, child_keyword)
                else:
                    # 未通过测试，一同降级
                    self.demote_to_decay_pool(child_id, child_keyword)
                    # 递归处理子关键词的子关键词
                    self.trigger_cascade_demotion(child_id)
                    
        except Exception as e:
            import traceback
            self.logger.error(f"触发连坐降级机制时出错: {str(e)}")
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")
    
    def independent_survival_test(self, child_id, child_keyword, parent_id):
        """独立生存测试
        
        Args:
            child_id: 子关键词ID
            child_keyword: 子关键词
            parent_id: 父关键词ID
            
        Returns:
            bool: 是否通过独立生存测试
        """
        try:
            # 获取子关键词的表现数据
            child_performance = self.get_keyword_performance_data(child_keyword)
            if not child_performance:
                return False
            
            # 获取父关键词
            parent_df = worker.execute_to_df(
                "SELECT keyword FROM root_keywords WHERE id = ?",
                [parent_id]
            )
            
            if parent_df.empty:
                return False
            
            parent_keyword = parent_df.iloc[0]['keyword']
            parent_performance = self.get_keyword_performance_data(parent_keyword)
            
            if not parent_performance:
                return False
            
            # 独立生存测试：子关键词历史峰值绝对搜索量 > 父关键词历史峰值绝对搜索量 * 1.2
            child_peak_absolute = child_performance['historical_peak_absolute']
            parent_peak_absolute = parent_performance['historical_peak_absolute']

            threshold = parent_peak_absolute * 1.2
            passed = child_peak_absolute > threshold

            self.logger.info(f"独立生存测试 '{child_keyword}': "
                           f"子历史峰值绝对搜索量={child_peak_absolute}k, 父历史峰值绝对搜索量={parent_peak_absolute}k, "
                           f"阈值={threshold:.1f}k, 结果={'通过' if passed else '未通过'}")

            return passed
            
        except Exception as e:
            import traceback
            self.logger.error(f"进行独立生存测试时出错: {str(e)}")
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")
            return False
    
    def make_independent(self, keyword_id, keyword):
        """使关键词独立门户
        
        Args:
            keyword_id: 关键词ID
            keyword: 关键词
        """
        try:
            result = worker.execute(
                "UPDATE root_keywords SET parent_id = NULL WHERE id = ?",
                [keyword_id]
            )
            
            if result.get('success', False):
                self.logger.info(f"关键词 '{keyword}' 已独立门户")
                return True
            else:
                self.logger.error(f"使关键词 '{keyword}' 独立门户失败: {result.get('error')}")
                return False
                
        except Exception as e:
            self.logger.error(f"使关键词 '{keyword}' 独立门户时出错: {str(e)}")
            return False
    
    def run_daily_review(self):
        """执行每日审查"""
        self.logger.info("开始执行每日三池审查")
        
        try:
            # 处理新生池
            self.process_newborn_pool()
            
            # 处理稳定池（可以设置为每周执行）
            # self.process_stable_pool()
            
            self.logger.info("每日三池审查完成")
            
        except Exception as e:
            self.logger.error(f"执行每日审查时出错: {str(e)}")
    
    def run_weekly_review(self):
        """执行每周审查"""
        self.logger.info("开始执行每周三池审查")
        
        try:
            # 处理稳定池
            self.process_stable_pool()
            
            self.logger.info("每周三池审查完成")
            
        except Exception as e:
            self.logger.error(f"执行每周审查时出错: {str(e)}")
