import requests
from bs4 import BeautifulSoup
from datetime import datetime, timedelta, UTC
import time
import re
from db import DBWorker
import json

def extract_wordle_answer(soup, keyword, extract_length=200):
    # 获取所有类型标签的文本内容
    text_content = ' '.join([
        tag.get_text() for tag in soup.find_all(['p', 'span', 'div', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'article'])
        if tag.get_text().strip()
    ])
    
    # 使用正则表达式匹配关键词后的内容
    match = re.search(fr'{keyword}(.*?)(?:\n|$)', text_content, re.IGNORECASE)
    
    if match:
        answer_text = match.group(1).strip()[:extract_length]  # 提取匹配到的内容的前200个字符
        return answer_text
    return None

def extract_wordle_solution(text_content, pattern):
    print(text_content)
    match = re.search(pattern, text_content, re.IGNORECASE)
    print(match)
    if match:
        answer_text = match.group(1).strip()
        return answer_text
    return None

def get_answer(date,root_url,keyword,pattern,extract_length=200):
    # 格式化日期为URL需要的格式，去掉一位数日期的前导零
    month = date.strftime("%B").lower()
    day = str(date.day)  # 直接转换为字符串，去掉前导零
    year = date.strftime("%Y")
    formatted_date = f"{month}-{day}-{year}"
    url = f"{root_url}{formatted_date}"
   
    
    try:
        response = requests.get(url)
        response.raise_for_status()  # 检查请求是否成功
        
        soup = BeautifulSoup(response.text, 'html.parser')
        answer_text = extract_wordle_answer(soup,keyword,extract_length)
        answer_text = extract_wordle_solution(answer_text,pattern)
        if answer_text:
            print(f"{date.strftime('%Y-%m-%d')}{url}: 获取成功")
            print(f"答案内容: {answer_text}")
            return answer_text
        else:
            print(f"{date.strftime('%Y-%m-%d')}{url}: 获取失败 - 未找到相关内容")
            return ''
            
    except requests.RequestException as e:
        print(f"{date.strftime('%Y-%m-%d')}{url}: 获取失败 - {str(e)}")
        # 新增：使用duckduckgo搜索
        search_query = f"mashable Wordle {month} {day} {year}"
        duck_url = duckduckgo_search(search_query)
        if duck_url:
            try:
                response = requests.get(duck_url)
                response.raise_for_status()
                soup = BeautifulSoup(response.text, 'html.parser')
                answer_text = extract_wordle_answer(soup,keyword,extract_length)
                answer_text = extract_wordle_solution(answer_text,pattern)
                if answer_text:
                    print(f"{date.strftime('%Y-%m-%d')}{duck_url}: 通过DuckDuckGo获取成功")
                    print(f"答案内容: {answer_text}")
                    return answer_text
                else:
                    print(f"{date.strftime('%Y-%m-%d')}{duck_url}: 通过DuckDuckGo获取失败 - 未找到相关内容")
                    return ''
            except requests.RequestException as e2:
                print(f"{date.strftime('%Y-%m-%d')}{duck_url}: 通过DuckDuckGo获取失败 - {str(e2)}")
                return ''
        return ''

def save_word_to_db(word,game_name,date):
    worker = DBWorker(base_url="https://worker-db-proxy-trends.jameschan617.workers.dev")
    worker.execute(f"INSERT INTO word_game_list (word, game_name, date) VALUES ('{word}', '{game_name}', '{date}');")
    # CREATE TABLE word_game_list (
    # id INTEGER PRIMARY KEY AUTOINCREMENT,
    # word TEXT NOT NULL,
    # game_name TEXT NOT NULL,
    # date DATE NOT NULL
# );
    pass

def main():
    root_url_wordle = "https://mashable.com/article/wordle-today-answer-"
    keyword_wordle = "The Wordle answer today is"
    #pattern_wordle = r'The Wordle answer today is(.*?)\.'
    #pattern_wordle = r"The solution to today/'s(.*?)is"
    pattern_wordle = r"The solution to today's Wordle is\.{3}(.*?)\."
    root_url_connections = "https://mashable.com/article/nyt-connections-hint-answer-today-"
    keyword_connections = "What is the answer to"

    root_url_crossword="https://mashable.com/article/nyt-mini-crossword-answers-hints-"
    keyword_crossword="Here are the clues and answers"
    # 获取当前日期
    date = datetime.now(UTC)#-timedelta(days=1)
    # 转换为纽约时间（EST，UTC-5）
    NewYork_Date = date - timedelta(hours=5)
    
    answer = get_answer(NewYork_Date,root_url_wordle,keyword_wordle,pattern_wordle)
    if answer:
        save_word_to_db(answer,'wordle',NewYork_Date)

        # get_answer(current_date,root_url_connections,keyword_connections,'')
        # get_answer(current_date,root_url_crossword,keyword_crossword)
        # 添加延时以避免请求过于频繁

# 新增DuckDuckGo搜索函数
def duckduckgo_search(query):
    """
    使用DuckDuckGo的API搜索，返回第一个搜索结果的URL。
    """
    url = f"https://duckduckgo.com/html/?q={requests.utils.quote(query)}"
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3'
    }
    try:
        resp = requests.get(url, headers=headers, timeout=10)
        resp.raise_for_status()
        soup = BeautifulSoup(resp.text, 'html.parser')
        results = soup.find_all('a', class_='result__a', href=True)
        if results:
            return results[0]['href']
        return None
    except Exception as e:
        print(f"DuckDuckGo搜索失败: {e}")
        return None

if __name__ == "__main__":
    main()
