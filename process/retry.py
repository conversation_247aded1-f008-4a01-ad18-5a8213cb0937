import logging
import time
import os
import sys
from db_ops import worker, get_config_value, save_interest_over_time_to_db
from trends_api import get_interest_over_time_by_word
from utils import setup_logger
from proxy import proxy_rotator


def get_failed_keywords(task_id: str):
    """获取指定任务中所有没有 interest_over_time 数据的关键词"""
    query = f"""
    SELECT keyword 
    FROM rising_keywords 
    WHERE task_id = ? 
    AND (keyword_interest_over_time IS NULL OR keyword_interest_over_time = '')
    """
    df = worker.execute_to_df(query, [task_id])
    return df['keyword'].tolist() if not df.empty else []

def retry_interest_over_time(task_id: str):
    """重试获取指定任务中所有失败关键词的 interest_over_time 数据"""
    # 设置日志
    logger = setup_logger(f"retry_{task_id}")
    logger.info(f"开始重试任务 {task_id} 的 interest_over_time 数据获取")
    
    # 获取失败的关键词列表
    failed_keywords = get_failed_keywords(task_id)
    if not failed_keywords:
        logger.info("没有需要重试的关键词")
        return
    
    logger.info(f"找到 {len(failed_keywords)} 个需要重试的关键词")
    
    # 获取参考关键词
    reference_word = get_config_value("reference_keywords", "animal generator")
    
    # 遍历每个关键词进行重试
    for keyword in failed_keywords:
        try:
            logger.info(f"正在重试获取关键词 '{keyword}' 的兴趣度数据...")
            data = get_interest_over_time_by_word(keyword, reference_word)
            logger.info(f"成功获取关键词 '{keyword}' 的兴趣度数据")
            
            result = save_interest_over_time_to_db(data, task_id, keyword, reference_word)
            if result.get('success', False):
                logger.info(f"成功保存关键词 '{keyword}' 的兴趣度数据")
            else:
                logger.error(f"保存关键词 '{keyword}' 的兴趣度数据失败: {result.get('error')}")
            
            # 添加延时，避免请求过于频繁
            time.sleep(5)
            
        except Exception as e:
            logger.error(f"重试关键词 '{keyword}' 时发生错误: {str(e)}")
            continue

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python retry.py <task_id>")
        sys.exit(1)
    
    task_id = sys.argv[1]
    retry_interest_over_time(task_id)
