import time
import logging
import random
from trendspy2 import Trends
from proxy import proxy_rotator
from db_ops import get_blacklist_words, get_wordle_answer, get_rising_threshold, get_config_value
from utils import get_fibonacci_wait_time
from analysis import normalize_interest_data, aggregate_hourly_to_daily

def get_related_by_keyword(keyword):
    """获取关键词的相关上升查询
    
    Args:
        keyword: 要查询的关键词
        
    Returns:
        pandas.DataFrame: 过滤后的上升查询数据
        
    Raises:
        Exception: 如果所有重试都失败
    """
    max_retries = int(get_config_value('max_retries', 13))
    initial_wait = int(get_config_value('initial_wait', 300))
    
    # 获取黑名单词汇和Wordle答案
    blacklist_words = get_blacklist_words()
    wordle_answers = get_wordle_answer()
    
    for attempt in range(max_retries):
        try:
            # 选择代理
            reason = f"查询'{keyword}'相关查询 (尝试{attempt+1}/{max_retries})"
            if attempt < 3:
                proxy_config = proxy_rotator.get_best_proxy(reason)
            else:
                proxy_config = proxy_rotator.get_next_proxy(reason)
                
            # 初始化Trends分析器
            analyzer = Trends(proxy=proxy_config)
            rising_queries = analyzer.related_queries(keyword=keyword, timeframe='now 7-d').get('rising', [])

            # 标记此代理成功
            proxy_rotator.mark_proxy_result(proxy_config, True)
            logging.info(f"成功获取关键词 '{keyword}' 的相关查询数据")

            # 过滤黑名单词汇和Wordle答案
            filtered_queries = rising_queries[~rising_queries['query'].str.lower().apply(lambda x: any(word in x for word in blacklist_words))]
            filtered_queries = filtered_queries[~filtered_queries['query'].str.lower().apply(lambda x: any(word in x for word in wordle_answers))]
            
            # 过滤长度大于60字符的查询
            filtered_queries = filtered_queries[filtered_queries['query'].str.len() <= 60]
            
            # 过滤低于阈值的查询
            rising_threshold = get_rising_threshold()
            filtered_queries = filtered_queries[filtered_queries['value'] > rising_threshold]
            
            return filtered_queries
            
        except Exception as e:
            # 标记此代理失败
            proxy_rotator.mark_proxy_result(proxy_config, False)
            
            # 最后一次尝试失败就抛出异常
            if attempt == max_retries - 1:
                raise Exception(f"获取关键词 '{keyword}' 的相关查询失败，已重试{max_retries}次: {str(e)}")
            
            # 计算等待时间
            wait_time = get_fibonacci_wait_time(attempt, initial_wait)
            
            # 添加随机性
            actual_wait = min(wait_time, 60 + random.randint(0, 30)) if attempt < 3 else wait_time
            
            # 截断过长的错误信息
            error_msg = str(e)
            if len(error_msg) > 50:
                error_msg = error_msg[:50] + "..."
            
            logging.warning(f"获取关键词 '{keyword}' 的相关查询失败: {error_msg}，将在{actual_wait}秒后重试(尝试{attempt+1}/{max_retries})")
            
            # 注意：移除了额外的代理轮换循环，让代理选择更加简单直观
            # 下次循环开始时会自动选择新的代理
            
            time.sleep(actual_wait)

def get_interest_over_time_by_word(keyword, reference_keyword):
    """获取关键词的兴趣度时间序列数据
    
    Args:
        keyword: 要查询的关键词
        reference_keyword: 参考关键词
        
    Returns:
        list: [keyword_daily_data, reference_daily_data]
        
    Raises:
        Exception: 如果所有重试都失败
    """
    max_retries = int(get_config_value('max_retries', 13))
    initial_wait = int(get_config_value('initial_wait', 300))
    
    for attempt in range(max_retries):
        try:
            keywords_with_ref = [keyword, reference_keyword]
            
            # 选择代理
            reason = f"查询'{keyword}'兴趣度数据 (尝试{attempt+1}/{max_retries})"
            if attempt < 3:
                proxy_config = proxy_rotator.get_best_proxy(reason)
            else:
                proxy_config = proxy_rotator.get_next_proxy(reason)
                
            # 初始化Trends分析器
            analyzer = Trends(proxy=proxy_config)
            interest_over_time = analyzer.interest_over_time(keywords=keywords_with_ref, timeframe="now 7-d")
            
            # 标记此代理成功
            proxy_rotator.mark_proxy_result(proxy_config, True)
            logging.info(f"成功获取关键词 '{keyword}' 的兴趣度数据")
            
            # 获取原始数据
            keyword_data = interest_over_time[keyword].tolist()
            reference_data = interest_over_time[reference_keyword].tolist()
            
            # 将小时级数据聚合为天级数据
            daily_keyword_sums = aggregate_hourly_to_daily(keyword_data)
            daily_reference_sums = aggregate_hourly_to_daily(reference_data)
            
            # 标准化数据
            normalized_keyword, normalized_reference = normalize_interest_data(
                daily_keyword_sums, daily_reference_sums
            )
            
            return [normalized_keyword, normalized_reference]
            
        except Exception as e:
            # 标记此代理失败
            proxy_rotator.mark_proxy_result(proxy_config, False)
            
            # 最后一次尝试失败就抛出异常
            if attempt == max_retries - 1:
                raise Exception(f"获取关键词 '{keyword}' 的兴趣度数据失败，已重试{max_retries}次: {str(e)}")
            
            # 计算等待时间
            wait_time = get_fibonacci_wait_time(attempt, initial_wait)
            
            # 添加随机性
            actual_wait = min(wait_time, 60 + random.randint(0, 30)) if attempt < 3 else wait_time
            
            # 截断过长的错误信息
            error_msg = str(e)
            if len(error_msg) > 50:
                error_msg = error_msg[:50] + "..."
            
            logging.warning(f"获取关键词 '{keyword}' 的兴趣度数据失败: {error_msg}，将在{actual_wait}秒后重试(尝试{attempt+1}/{max_retries})")
            
            # 注意：移除了额外的代理轮换循环，让代理选择更加简单直观
            # 下次循环开始时会自动选择新的代理
            
            time.sleep(actual_wait)

def get_interest_over_time_batch(keywords_list, reference_keyword):
    """批量获取关键词的兴趣度时间序列数据

    实现5词批量查询：1个基准词 + 4个目标关键词

    Args:
        keywords_list: 目标关键词列表 (最多4个)
        reference_keyword: 参考关键词

    Returns:
        dict: {keyword: [normalized_daily_data], reference_keyword: [normalized_daily_data]}

    Raises:
        Exception: 如果所有重试都失败
    """
    if len(keywords_list) > 4:
        raise ValueError("批量查询最多支持4个关键词")

    max_retries = int(get_config_value('max_retries', 13))
    initial_wait = int(get_config_value('initial_wait', 300))

    # 构建查询关键词列表：基准词 + 目标关键词
    all_keywords = [reference_keyword] + keywords_list

    for attempt in range(max_retries):
        try:
            # 选择代理
            reason = f"批量查询{len(keywords_list)}个关键词兴趣度数据 (尝试{attempt+1}/{max_retries})"
            if attempt < 3:
                proxy_config = proxy_rotator.get_best_proxy(reason)
            else:
                proxy_config = proxy_rotator.get_next_proxy(reason)

            # 初始化Trends分析器
            analyzer = Trends(proxy=proxy_config)
            interest_over_time = analyzer.interest_over_time(keywords=all_keywords, timeframe="now 7-d")

            # 标记此代理成功
            proxy_rotator.mark_proxy_result(proxy_config, True)
            logging.info(f"成功批量获取{len(keywords_list)}个关键词的兴趣度数据")

            # 处理所有关键词的数据
            result = {}

            # 先聚合所有关键词的小时数据为天数据
            all_daily_data = {}

            # 处理基准词数据
            reference_data = interest_over_time[reference_keyword].tolist()
            daily_reference_sums = aggregate_hourly_to_daily(reference_data)
            all_daily_data[reference_keyword] = daily_reference_sums

            # 处理每个目标关键词
            for keyword in keywords_list:
                keyword_data = interest_over_time[keyword].tolist()
                daily_keyword_sums = aggregate_hourly_to_daily(keyword_data)
                all_daily_data[keyword] = daily_keyword_sums

            # 找到所有词所有天中的全局最大值
            all_values = []
            for daily_data in all_daily_data.values():
                all_values.extend(daily_data)
            global_max = max(all_values) if all_values else 1

            # 对所有关键词进行统一标准化
            for keyword, daily_data in all_daily_data.items():
                if global_max > 0:
                    normalized_data = [int(round(x * 100 / global_max)) for x in daily_data]
                else:
                    normalized_data = daily_data
                result[keyword] = normalized_data

            return result

        except Exception as e:
            # 标记此代理失败
            proxy_rotator.mark_proxy_result(proxy_config, False)

            # 最后一次尝试失败就抛出异常
            if attempt == max_retries - 1:
                raise Exception(f"批量获取关键词兴趣度数据失败，已重试{max_retries}次: {str(e)}")

            # 计算等待时间
            wait_time = get_fibonacci_wait_time(attempt, initial_wait)

            # 添加随机性
            actual_wait = min(wait_time, 60 + random.randint(0, 30)) if attempt < 3 else wait_time

            # 截断过长的错误信息
            error_msg = str(e)
            if len(error_msg) > 50:
                error_msg = error_msg[:50] + "..."

            logging.warning(f"批量获取关键词兴趣度数据失败: {error_msg}，将在{actual_wait}秒后重试(尝试{attempt+1}/{max_retries})")

            time.sleep(actual_wait)