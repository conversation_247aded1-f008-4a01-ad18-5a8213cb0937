"""
代理管理模块

该模块提供了代理IP的管理、轮换和评分功能，用于支持Google Trends API的稳定访问。
主要解决问题：
1. 分散请求到多个代理IP，避免单一IP被封禁
2. 自动选择性能最佳的代理
3. 提供代理轮换策略

主要组件:
- ProxyRotator: 代理轮换器，管理代理池并提供智能选择功能
"""

import random
import time
import logging

class ProxyRotator:
    def __init__(self, proxy_list):
        self.proxy_list = proxy_list
        self.current_index = 0
        self.last_used_proxy = None
        # 跟踪每个代理的成功/失败次数
        self.proxy_stats = {proxy: {'success': 0, 'failure': 0, 'last_used': 0} for proxy in proxy_list}
        
    def get_next_proxy(self, reason="默认轮换"):
        """获取下一个代理配置，确保不会连续使用同一个代理"""
        # 如果只有一个代理，直接返回
        if len(self.proxy_list) == 1:
            proxy = self.proxy_list[0]
        else:
            # 确保不会选择上次使用的代理
            start_index = self.current_index
            proxy = self.proxy_list[self.current_index]
            
            # 如果代理与上次使用的相同，则移动到下一个
            if proxy == self.last_used_proxy:
                self.current_index = (self.current_index + 1) % len(self.proxy_list)
                proxy = self.proxy_list[self.current_index]
            
            # 更新索引，为下次准备
            self.current_index = (self.current_index + 1) % len(self.proxy_list)
        
        self.last_used_proxy = proxy
        self.proxy_stats[proxy]['last_used'] = time.time()
        proxy_index = self.proxy_list.index(proxy) + 1  # 从1开始计数，更易读
        logging.info(f"使用代理[{proxy_index}/{len(self.proxy_list)}]: {proxy} (原因: {reason})")
        return {
            "http": proxy,
            "https": proxy
        }
    
    def get_random_proxy(self, reason="随机选择"):
        """随机获取一个代理配置，避免选择上次使用的代理"""
        if len(self.proxy_list) == 1:
            proxy = self.proxy_list[0]
        else:
            # 创建一个不包含上次使用的代理的列表
            available_proxies = [p for p in self.proxy_list if p != self.last_used_proxy]
            proxy = random.choice(available_proxies)
        
        self.last_used_proxy = proxy
        self.proxy_stats[proxy]['last_used'] = time.time()
        proxy_index = self.proxy_list.index(proxy) + 1
        logging.info(f"随机选择代理[{proxy_index}/{len(self.proxy_list)}]: {proxy} (原因: {reason})")
        return {
            "http": proxy,
            "https": proxy
        }
        
    def get_best_proxy(self, reason="选择最佳代理"):
        """获取成功率最高的代理，避免选择上次使用的代理"""
        best_proxy = None
        best_score = -1
        second_best_proxy = None
        second_best_score = -1
        
        for proxy, stats in self.proxy_stats.items():
            success = stats['success'] + 1  # 加1避免除以0
            total = success + stats['failure']
            # 计算成功率，并考虑总使用次数和最后使用时间
            success_rate = success / total
            time_factor = 1.0
            
            # 如果该代理30分钟内被使用过，适当降低其权重以实现负载均衡
            time_since_last_used = time.time() - stats['last_used'] if stats['last_used'] > 0 else 1800
            if time_since_last_used < 1800:  # 30分钟 = 1800秒
                time_factor = time_since_last_used / 1800
                
            score = success_rate * (0.7 + 0.3 * time_factor)
            
            # 跟踪最佳和次佳代理
            if score > best_score:
                second_best_score = best_score
                second_best_proxy = best_proxy
                best_score = score
                best_proxy = proxy
            elif score > second_best_score:
                second_best_score = score
                second_best_proxy = proxy
        
        # 如果最佳代理是上次使用的，且有可用的次佳代理，则使用次佳代理
        if best_proxy == self.last_used_proxy and second_best_proxy is not None:
            best_proxy = second_best_proxy
            logging.debug(f"最佳代理与上次相同，切换到次佳代理")
        
        # 如果没有找到代理，使用随机代理
        if best_proxy is None:
            logging.info("没有找到最佳代理，使用随机代理")
            return self.get_random_proxy("没有最佳代理可用")
        
        self.last_used_proxy = best_proxy
        self.proxy_stats[best_proxy]['last_used'] = time.time()
        proxy_index = self.proxy_list.index(best_proxy) + 1
        logging.info(f"使用最佳代理[{proxy_index}/{len(self.proxy_list)}]: {best_proxy} (原因: {reason})")
        return {
            "http": best_proxy,
            "https": best_proxy
        }
    
    def mark_proxy_result(self, proxy_config, success):
        """标记代理请求的结果"""
        proxy = proxy_config.get('http', None)
        if proxy and proxy in self.proxy_stats:
            if success:
                self.proxy_stats[proxy]['success'] += 1
                logging.debug(f"代理请求成功 (+1)")
            else:
                self.proxy_stats[proxy]['failure'] += 1
                logging.debug(f"代理请求失败 (+1)")

    def get_proxy_status(self):
        """获取所有代理的状态信息，用于调试"""
        status = {}
        for i, proxy in enumerate(self.proxy_list):
            stats = self.proxy_stats[proxy]
            status[f"代理{i+1}"] = {
                '地址': proxy,
                '成功率': f"{stats['success'] / (stats['success'] + stats['failure']) * 100:.1f}%" if (stats['success'] + stats['failure']) > 0 else "0.0%",
                '总请求数': stats['success'] + stats['failure'],
                '成功次数': stats['success'],
                '失败次数': stats['failure']
            }
        return status

# 默认代理列表
DEFAULT_PROXY_LIST = [
    "http://brd-customer-hl_11c875ea-zone-datacenter_proxy2:<EMAIL>:33335",
    "http://brd-customer-hl_11c875ea-zone-datacenter_proxy3:<EMAIL>:33335",
    "http://brd-customer-hl_11c875ea-zone-datacenter_proxy4:<EMAIL>:33335",
    "http://brd-customer-hl_11c875ea-zone-datacenter_proxy5:<EMAIL>:33335",
    "http://brd-customer-hl_11c875ea-zone-datacenter_proxy6:<EMAIL>:33335",
    "http://brd-customer-hl_11c875ea-zone-datacenter_proxy7:<EMAIL>:33335",
    "http://brd-customer-hl_11c875ea-zone-datacenter_proxy8:<EMAIL>:33335",
    "http://brd-customer-hl_11c875ea-zone-datacenter_proxy9:<EMAIL>:33335",
    "http://brd-customer-hl_11c875ea-zone-datacenter_proxy10:<EMAIL>:33335",
    "http://brd-customer-hl_11c875ea-zone-datacenter_proxy21:<EMAIL>:33335",
    "http://brd-customer-hl_11c875ea-zone-datacenter_proxy20:<EMAIL>:33335",
    "http://brd-customer-hl_11c875ea-zone-datacenter_proxy19:<EMAIL>:33335",
    "http://brd-customer-hl_11c875ea-zone-datacenter_proxy18:<EMAIL>:33335",
    "http://brd-customer-hl_11c875ea-zone-datacenter_proxy17:<EMAIL>:33335",
    "http://brd-customer-hl_11c875ea-zone-datacenter_proxy16:<EMAIL>:33335",
    "http://brd-customer-hl_11c875ea-zone-datacenter_proxy15:<EMAIL>:33335",
    "http://brd-customer-hl_11c875ea-zone-datacenter_proxy14:<EMAIL>:33335",
    "http://brd-customer-hl_11c875ea-zone-datacenter_proxy13:<EMAIL>:33335",
    "http://brd-customer-hl_11c875ea-zone-datacenter_proxy12:<EMAIL>:33335",
    "http://brd-customer-hl_11c875ea-zone-datacenter_proxy11:<EMAIL>:33335"
]

# 初始化默认代理轮转器实例
proxy_rotator = ProxyRotator(DEFAULT_PROXY_LIST) 