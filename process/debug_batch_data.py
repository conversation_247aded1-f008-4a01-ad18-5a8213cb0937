#!/usr/bin/env python3
"""
调试批次数据脚本

查看数据库中的批次数据，模拟 main.py 的查询逻辑
"""

import logging
from db_ops import get_pending_keywords_for_batch_processing, worker

# 设置日志
logging.basicConfig(level=logging.DEBUG)
logging.getLogger('urllib3.connectionpool').setLevel(logging.WARNING)

def debug_batch_data(task_id):
    """调试批次数据"""
    print(f"=== 调试批次数据 ===")
    print(f"Task ID: {task_id}")
    
    # 模拟 main.py 的批量处理逻辑
    batch_size = 4
    batch_count = 0
    
    while True:
        batch_count += 1
        print(f"\n--- 第{batch_count}批 ---")
        
        # 获取批次数据（和 main.py 完全一样的调用）
        keywords_batch = get_pending_keywords_for_batch_processing(task_id, batch_size)
        
        if not keywords_batch:
            print("没有更多待处理的关键词")
            break
        
        print(f"批次大小: {len(keywords_batch)}")
        print(f"批次数据类型: {type(keywords_batch)}")
        
        # 详细检查每个项目
        for i, item in enumerate(keywords_batch):
            print(f"  项目 {i+1}:")
            print(f"    类型: {type(item)}")
            print(f"    内容: {item}")
            
            if isinstance(item, dict):
                print(f"    键: {list(item.keys())}")
                print(f"    keyword: {item.get('keyword', 'NOT_FOUND')}")
                print(f"    root_keyword: {item.get('root_keyword', 'NOT_FOUND')}")
            else:
                print(f"    ❌ 不是字典类型!")
        
        # 限制只查看前10批，避免无限循环
        if batch_count >= 10:
            print("\n已查看10批，停止")
            break

def check_database_directly(task_id):
    """直接查看数据库数据"""
    print(f"\n=== 直接查看数据库 ===")
    
    try:
        # 查看所有待处理的关键词
        df = worker.execute_to_df(
            """SELECT keyword, root_keyword, 
                      keyword_interest_over_time,
                      created_at
               FROM rising_keywords 
               WHERE task_id = ? 
               ORDER BY created_at DESC""",
            [task_id]
        )
        
        print(f"总记录数: {len(df)}")
        
        if not df.empty:
            print(f"\n前10条记录:")
            for i, (_, row) in enumerate(df.head(10).iterrows()):
                keyword = row['keyword']
                root_keyword = row['root_keyword']
                interest_data = row['keyword_interest_over_time']
                created_at = row['created_at']
                
                status = "已处理" if interest_data else "待处理"
                print(f"  {i+1}. {keyword} (来源: {root_keyword}) - {status} - {created_at}")
        
        # 查看待处理的关键词
        pending_df = worker.execute_to_df(
            """SELECT keyword, root_keyword 
               FROM rising_keywords 
               WHERE task_id = ? 
               AND (keyword_interest_over_time IS NULL OR keyword_interest_over_time = '')
               ORDER BY created_at DESC""",
            [task_id]
        )
        
        print(f"\n待处理关键词数: {len(pending_df)}")
        
        if not pending_df.empty:
            print(f"待处理关键词列表:")
            for i, (_, row) in enumerate(pending_df.iterrows()):
                keyword = row['keyword']
                root_keyword = row['root_keyword']
                print(f"  {i+1}. {keyword} (来源: {root_keyword})")
        
    except Exception as e:
        print(f"数据库查询失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) != 2:
        print("使用方法: python debug_batch_data.py <task_id>")
        print("例如: python debug_batch_data.py 20250722_13")
        return
    
    task_id = sys.argv[1]
    
    # 检查数据库数据
    check_database_directly(task_id)
    
    # 调试批次数据
    debug_batch_data(task_id)

if __name__ == "__main__":
    main()
