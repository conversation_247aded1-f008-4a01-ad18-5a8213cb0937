import requests
import logging

# 飞书机器人Webhook URL
FEISHU_WEBHOOK_URL = "https://open.feishu.cn/open-apis/bot/v2/hook/7e2ea36b-c640-4ce2-8c76-44a0e7d6c44f"

def send_feishu_message(keyword, root_keyword,reference_keyword):
    """发送飞书消息通知
    
    Args:
        keyword: 关键词
        reference_keyword: 参考关键词
        
    Returns:
        dict: 请求响应
    """
    headers = {
        "Content-Type": "application/json"
    }
    
    # 处理URL编码
    keyword_title = keyword
    keyword = keyword.replace(' ', '%20')
    reference_keyword = reference_keyword.replace(' ', '%20')
    
    # 构建消息内容
    data = {
        "msg_type": "text", 
        "content": {
            "text": f"【{root_keyword}】\n【{keyword_title}】\n 7days: https://trends.google.com/trends/explore?date=now%207-d&q={keyword},{reference_keyword} \n 30 days: https://trends.google.com/trends/explore?date=today%201-m&q={keyword},{reference_keyword}"
        }
    }
    
    # 发送请求
    try:
        response = requests.post(FEISHU_WEBHOOK_URL, headers=headers, json=data)
        return response.json()
    except Exception as e:
        logging.error(f"发送飞书消息失败: {str(e)}")
        return {"error": str(e)}

def send_error_notification(message, task_id=None):
    """发送错误通知
    
    Args:
        message: 错误消息
        task_id: 任务ID (可选)
    """
    task_info = f"任务 {task_id}" if task_id else "未知任务"
    
    data = {
        "msg_type": "text", 
        "content": {
            "text": f"【错误通知】{task_info}\n{message}"
        }
    }
    
    try:
        headers = {"Content-Type": "application/json"}
        requests.post(FEISHU_WEBHOOK_URL, headers=headers, json=data)
    except Exception as e:
        logging.error(f"发送错误通知失败: {str(e)}")

def send_task_completion_notification(task_id, stats):
    """发送任务完成通知
    
    Args:
        task_id: 任务ID
        stats: 任务统计数据
    """
    data = {
        "msg_type": "text", 
        "content": {
            "text": f"【任务完成】{task_id}\n处理关键词: {stats.get('total', 0)}个\n有效结果: {stats.get('successful', 0)}个\n"
        }
    }
    
    try:
        headers = {"Content-Type": "application/json"}
        requests.post(FEISHU_WEBHOOK_URL, headers=headers, json=data)
    except Exception as e:
        logging.error(f"发送任务完成通知失败: {str(e)}") 