import requests
import pandas as pd
from typing import Dict, Any, Optional, Union

class DBWorker:
    def __init__(self, base_url: str,api_key: str=""):
        """
        Initialize the DBWorker with API key and base URL.
        
        Args:
            api_key (str): The API key for authentication
            base_url (str): The base URL of the worker endpoint
        """
        self.api_key = api_key
        self.base_url = base_url
        self.headers = {
            'X-API-Key': api_key,
            'Content-Type': 'application/json'
        }

    def execute(self, query: str, params: Optional[list] = None) -> Dict[str, Any]:
        """
        Execute a database query through the worker endpoint.
        
        Args:
            query (str): The SQL query to execute
            params (Optional[list]): List of parameters for parameterized query
            
        Returns:
            Dict[str, Any]: The response from the worker endpoint
            
        Raises:
            requests.exceptions.RequestException: If the request fails
        """
        url = f"{self.base_url}/execute"
        payload = {"query": query}
        if params is not None:
            payload["params"] = params
        #         # 添加代理设置
        # proxies = {
        #     'http': 'http://127.0.0.1:1082',
        #     'https': 'http://127.0.0.1:1082'
        # }
        response = requests.post(url, headers=self.headers, json=payload)#,proxies=proxies)
        # response.raise_for_status()  # Raise an exception for bad status codes
        
        return response.json()

    def execute_to_df(self, query: str, params: Optional[list] = None) -> pd.DataFrame:
        """
        Execute a database query and return the results as a pandas DataFrame.
        For non-SELECT queries, returns a DataFrame with metadata.
        
        Args:
            query (str): The SQL query to execute
            params (Optional[list]): List of parameters for parameterized query
            
        Returns:
            pd.DataFrame: DataFrame containing either query results or metadata
        """
        response = self.execute(query, params)

        # For SELECT queries with results
        if isinstance(response, list):
            return pd.DataFrame(response)
        elif isinstance(response, dict) and 'success' in response and response['success']:
            # 检查是否有查询结果
            if 'result' in response and 'results' in response['result'] and response['result']['results']:
                return pd.DataFrame(response['result']['results'])
            # 如果是SELECT查询但没有结果，返回空DataFrame
            elif query.strip().upper().startswith('SELECT'):
                return pd.DataFrame()
            # 对于非SELECT查询，返回元数据
            elif 'result' in response and 'meta' in response['result']:
                meta = response['result']['meta']
                meta_df = pd.DataFrame([{
                    'success': response['success'],
                    'changes': meta['changes'],
                    'last_row_id': meta['last_row_id'],
                    'rows_read': meta['rows_read'],
                    'rows_written': meta['rows_written'],
                    'duration': meta['duration']
                }])
                return meta_df

        # 如果没有结果，返回空的 DataFrame
        return pd.DataFrame()

# Example usage:
if __name__ == "__main__":
    # Initialize the worker with your API key
    worker = DBWorker(base_url="https://worker-db-proxy-trends.jameschan617.workers.dev")
    
    # Execute a query and get DataFrame
    try:
        #df = worker.execute_to_df("select * from root_keywords")
        df = worker.execute_to_df("INSERT INTO root_keywords (id, keyword, status, created_at, updated_at) VALUES (5, 'example_keyword3', 1, strftime('%s', 'now'), strftime('%s', 'now'));")
        print("DataFrame result:")
        print(df)
    except requests.exceptions.RequestException as e:
        print(f"Error executing query: {e}")