#!/usr/bin/env python3
"""
三池审查脚本

用于定期执行三池处理的审查任务：
- 每日审查：处理新生池中观察期满的关键词
- 每周审查：处理稳定池中的关键词

使用方法：
python pool_review.py daily    # 执行每日审查
python pool_review.py weekly   # 执行每周审查
python pool_review.py both     # 执行完整审查
"""

import sys
import logging
import argparse
from datetime import datetime
from pool_manager import PoolManager
from utils import setup_logger

def setup_review_logger():
    """设置审查日志"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f"pool_review_{timestamp}"
    return setup_logger(log_filename)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='三池审查脚本')
    parser.add_argument('mode', choices=['daily', 'weekly', 'both'], 
                       help='审查模式: daily(每日), weekly(每周), both(完整)')
    parser.add_argument('--verbose', '-v', action='store_true', 
                       help='详细日志输出')
    
    args = parser.parse_args()
    
    # 设置日志
    logger = setup_review_logger()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    logger.info(f"开始执行三池审查，模式: {args.mode}")
    
    try:
        # 创建池管理器
        pool_manager = PoolManager()
        
        if args.mode == 'daily':
            pool_manager.run_daily_review()
        elif args.mode == 'weekly':
            pool_manager.run_weekly_review()
        elif args.mode == 'both':
            pool_manager.run_daily_review()
            pool_manager.run_weekly_review()
        
        logger.info("三池审查执行完成")
        
    except Exception as e:
        logger.error(f"三池审查执行失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
