from datetime import datetime
import time
from db import DBWorker
from datetime import timezone, timedelta

class TaskManager:
    def __init__(self, base_url="https://worker-db-proxy-trends.jameschan617.workers.dev"):
        self.worker = DBWorker(base_url=base_url)
        self.beijing_tz = timezone(timedelta(hours=8))
        
    def _get_timestamp(self):
        return int(time.time())
    
    def _generate_task_id(self):
        return datetime.now(self.beijing_tz).strftime("%Y%m%d_%H")
    
    def create_task(self):
        """创建新任务"""
        task_id = self._generate_task_id()
        created_at = self._get_timestamp()
        
        # 先尝试插入新记录
        insert_result = self.worker.execute(
            "INSERT INTO tasks (id, status, created_at) VALUES (?, ?, ?)",
            [task_id, "created", created_at]
        )
        
        # 如果插入失败（记录已存在），则尝试更新
        if not insert_result.get('success', False):
            update_result = self.worker.execute(
                "UPDATE tasks SET status = ?, created_at = ? WHERE id = ?",
                ["created", created_at, task_id]
            )
            return task_id if update_result.get('success', False) else None
        
        return task_id
    
    def start_task(self, task_id):
        """开始任务"""
        started_at = self._get_timestamp()
        
        result = self.worker.execute(
            "UPDATE tasks SET status = ?, started_at = ? WHERE id = ?",
            ["running", started_at, task_id]
        )
        
        return result.get('success', False)
    
    def complete_task(self, task_id, success=True, error_message=None):
        """完成任务"""
        completed_at = self._get_timestamp()
        status = "completed" if success else "failed"
        
        result = self.worker.execute(
            "UPDATE tasks SET status = ?, completed_at = ?, error_message = ? WHERE id = ?",
            [status, completed_at, error_message, task_id]
        )
        
        return result.get('success', False)
    
    def fail_task(self, task_id, error_message=""):
        """将任务标记为失败"""
        return self.complete_task(task_id, success=False, error_message=error_message)

# 使用示例
if __name__ == "__main__":
    task_manager = TaskManager()
    
    # 创建任务
    task_id = task_manager.create_task()
    if task_id:
        print(f"Created task: {task_id}")
        
        # 开始任务
        if task_manager.start_task(task_id):
            print("Task started")
            
            # 模拟任务执行
            time.sleep(2)
            
            # 完成任务
            if task_manager.complete_task(task_id):
                print("Task completed")