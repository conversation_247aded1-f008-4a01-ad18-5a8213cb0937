from db import DBWorker 
# from trends import TrendsAnalyzer
from trendspy2 import Trends, <PERSON>chP<PERSON><PERSON>
from typing import Dict, Any, Optional
import pandas as pd
from tasks import TaskManager
import time
import random
import requests
import logging
import os
from datetime import datetime
import pytz

class ProxyRotator:
    def __init__(self, proxy_list):
        self.proxy_list = proxy_list
        self.current_index = 0
        # 跟踪每个代理的成功/失败次数
        self.proxy_stats = {proxy: {'success': 0, 'failure': 0, 'last_used': 0} for proxy in proxy_list}
        
    def get_next_proxy(self):
        """获取下一个代理配置"""
        proxy = self.proxy_list[self.current_index]
        self.current_index = (self.current_index + 1) % len(self.proxy_list)
        self.proxy_stats[proxy]['last_used'] = time.time()
        logging.info(f"使用next代理: {proxy}")
        return {
            "http": proxy,
            "https": proxy
        }
    
    def get_random_proxy(self):
        """随机获取一个代理配置"""
        proxy = random.choice(self.proxy_list)
        self.proxy_stats[proxy]['last_used'] = time.time()
        print(f"随机获取代理: {proxy}")
        return {
            "http": proxy,
            "https": proxy
        }
        
    def get_best_proxy(self):
        """获取成功率最高的代理"""
        best_proxy = None
        best_score = -1
        
        for proxy, stats in self.proxy_stats.items():
            success = stats['success'] + 1  # 加1避免除以0
            total = success + stats['failure']
            # 计算成功率，并考虑总使用次数和最后使用时间
            success_rate = success / total
            time_factor = 1.0
            
            # 如果该代理30分钟内被使用过，适当降低其权重以实现负载均衡
            time_since_last_used = time.time() - stats['last_used'] if stats['last_used'] > 0 else 1800
            if time_since_last_used < 1800:  # 30分钟 = 1800秒
                time_factor = time_since_last_used / 1800
                
            score = success_rate * (0.7 + 0.3 * time_factor)
            
            if score > best_score:
                best_score = score
                best_proxy = proxy
        
        # 如果没有找到最佳代理（可能所有代理都没有使用过），使用随机代理
        if best_proxy is None:
            return self.get_random_proxy()
        
        self.proxy_stats[best_proxy]['last_used'] = time.time()
        logging.info(f"使用最佳代理: {best_proxy}")
        return {
            "http": best_proxy,
            "https": best_proxy
        }
    
    def mark_proxy_result(self, proxy_config, success):
        """标记代理请求的结果"""
        proxy = proxy_config.get('http', None)
        if proxy and proxy in self.proxy_stats:
            if success:
                self.proxy_stats[proxy]['success'] += 1
            else:
                self.proxy_stats[proxy]['failure'] += 1

DBWorkerUrl="https://worker-db-proxy-trends.jameschan617.workers.dev"
worker = DBWorker(base_url=DBWorkerUrl)
proxy_list=[
"http://brd-customer-hl_11c875ea-zone-datacenter_proxy2:<EMAIL>:33335",
"http://brd-customer-hl_11c875ea-zone-datacenter_proxy3:<EMAIL>:33335",
"http://brd-customer-hl_11c875ea-zone-datacenter_proxy4:<EMAIL>:33335",
"http://brd-customer-hl_11c875ea-zone-datacenter_proxy5:<EMAIL>:33335",
"http://brd-customer-hl_11c875ea-zone-datacenter_proxy6:<EMAIL>:33335",
"http://brd-customer-hl_11c875ea-zone-datacenter_proxy7:<EMAIL>:33335",
"http://brd-customer-hl_11c875ea-zone-datacenter_proxy8:<EMAIL>:33335",
"http://brd-customer-hl_11c875ea-zone-datacenter_proxy9:<EMAIL>:33335",
"http://brd-customer-hl_11c875ea-zone-datacenter_proxy10:<EMAIL>:33335"
]

# 初始化代理轮转器
proxy_rotator = ProxyRotator(proxy_list)

def get_root_keywords():
    df = worker.execute_to_df("select * from root_keywords")
    return df
    # return df.to_dict(orient="records")
def get_rising_keywords(task_id):
    df = worker.execute_to_df(f"select keyword from rising_keywords where task_id='{task_id}' ")
    print(df)
    keywords=df['keyword'].tolist()  # 转换为Python列表
    return keywords
def get_config_value(config_key: str, default_value=None):

    df = worker.execute_to_df(f"select config_value from system_configs where config_key='{config_key}'")
    if df.empty:
        return default_value
    return df.iloc[0]['config_value']
def get_reference_word():

    df = worker.execute_to_df("select * from reference_keywords")
    return df
def get_rising_threshold():
    return int(get_config_value('rising_threshold', 1000))

def get_fibonacci_wait_time(n, initial_wait):
    """计算优化版的斐波那契等待时间，更适合网络重试场景"""
    max_wait = int(get_config_value('max_wait', 5400))
    
    # 对前几次重试使用较小的等待时间
    if n <= 0:
        return min(initial_wait, max_wait)
    elif n == 1:
        return min(initial_wait * 1.5, max_wait)  # 第二次稍微增加一点等待时间
    elif n == 2:
        return min(initial_wait * 2, max_wait)    # 第三次加倍等待时间
    
    # 从第四次开始使用修改版的斐波那契序列
    # 这样可以让等待时间增长更平滑
    a, b = 2, 3  # 从2,3开始而不是1,1
    for _ in range(3, n + 1):
        a, b = b, a + b
    
    # 添加一点随机性来避免可预测的模式
    jitter = random.uniform(0.8, 1.2)  # 增加±20%的随机波动
    return min(initial_wait * b * jitter, max_wait)

def get_blacklist_words():
    """获取黑名单词汇列表"""
    df = worker.execute_to_df("SELECT word FROM blacklist_words WHERE is_active = 1")
    return [word.lower() for word in df['word'].tolist()]
def get_wordle_answer():
    df = worker.execute_to_df("select word from word_game_list where game_name='wordle' and date > date('now','-3 days');")
    return [word.lower() for word in df['word'].tolist()]
def get_related_by_keyword(keyword):
    max_retries = int(get_config_value('max_retries', 13))
    initial_wait = int(get_config_value('initial_wait', 300))
    
    # 获取黑名单词汇
    blacklist_words = get_blacklist_words()
    wordle_answers = get_wordle_answer()
    for attempt in range(max_retries):
        try:
            # 前几次尝试用最佳代理，失败多次后随机选择
            if attempt < 3:
                proxy_config = proxy_rotator.get_best_proxy()
            else:
                proxy_config = proxy_rotator.get_next_proxy()
                
            analyzer = Trends(proxy=proxy_config)
            rising_queries = analyzer.related_queries(keyword=keyword, timeframe='now 7-d').get('rising', [])

            # 标记此代理成功
            proxy_rotator.mark_proxy_result(proxy_config, True)

            # 过滤掉包含黑名单词汇的查询
            filtered_queries = rising_queries[~rising_queries['query'].str.lower().apply(lambda x: any(word in x for word in blacklist_words))]
            filtered_queries = filtered_queries[~rising_queries['query'].str.lower().apply(lambda x: any(word in x for word in wordle_answers))]
            ##长度大于60字符的不要，丢弃
            filtered_queries = filtered_queries[filtered_queries['query'].str.len() <= 60]
            rising_threshold = get_rising_threshold()
            filtered_queries = filtered_queries[filtered_queries['value'] > rising_threshold]
            return filtered_queries
        except Exception as e:
            # 标记此代理失败
            proxy_rotator.mark_proxy_result(proxy_config, False)
            
            if attempt == max_retries - 1:
                raise Exception(f"获取关键词 '{keyword}' 的相关查询失败，已重试{max_retries}次: {str(e)}")
            
            # 计算等待时间
            wait_time = get_fibonacci_wait_time(attempt, initial_wait)
            
            # 根据等待时间决定轮换代理的策略
            # 如果等待时间较长，优先尝试更多代理而非长时间等待
            extra_proxy_rotations = min(5, 1 + attempt // 2)  # 随着尝试次数增加，增加额外代理轮换次数
            
            # 添加一些随机性以避免被识别为机器人
            actual_wait = min(wait_time, 60 + random.randint(0, 30)) if attempt < 3 else wait_time
            
            logging.warning(f"获取关键词 '{keyword}' 的相关查询失败，正在切换{extra_proxy_rotations}次代理，{actual_wait}秒后重试: {str(e)}")
            
            # 多轮换几次代理
            for _ in range(extra_proxy_rotations):
                proxy_rotator.get_next_proxy()
                
            time.sleep(actual_wait)

def get_interest_over_time_by_word(keyword, reference_keyword):
    max_retries = int(get_config_value('max_retries', 13))
    initial_wait = int(get_config_value('initial_wait', 300))
    
    for attempt in range(max_retries):
        try:
            keywords_with_ref = [keyword, reference_keyword]
            # 前几次尝试用最佳代理，失败多次后随机选择
            if attempt < 3:
                proxy_config = proxy_rotator.get_best_proxy()
            else:
                proxy_config = proxy_rotator.get_next_proxy()
                
            analyzer = Trends(proxy=proxy_config)
            interest_over_time = analyzer.interest_over_time(keywords=keywords_with_ref,timeframe="now 7-d")
            
            # 标记此代理成功
            proxy_rotator.mark_proxy_result(proxy_config, True)
            
            # 获取原始数据
            keyword_data = interest_over_time[keyword].tolist()
            reference_data = interest_over_time[reference_keyword].tolist()
            
            # 按天加总数据（每天24小时）
            daily_keyword_sums = []
            daily_reference_sums = []
            
            # 修改为明确的7天循环
            for day in range(7):
                start_idx = day * 24
                end_idx = start_idx + 24
                # 计算每天的总搜索量
                daily_keyword_sums.append(round(sum(keyword_data[start_idx:end_idx]),1))
                daily_reference_sums.append(round(sum(reference_data[start_idx:end_idx]),1))
            
            # 合并两组数据，找到所有数据中的最大值
            all_sums = daily_keyword_sums + daily_reference_sums
            global_max = max(all_sums)
            
            # 按最高值为100进行指数化
            if global_max > 0:
                daily_keyword_sums = [x * 100 / global_max for x in daily_keyword_sums]
                daily_reference_sums = [x * 100 / global_max for x in daily_reference_sums]
            
            return [daily_keyword_sums, daily_reference_sums]
        except Exception as e:
            # 标记此代理失败
            proxy_rotator.mark_proxy_result(proxy_config, False)
            
            if attempt == max_retries - 1:  # 最后一次尝试
                raise Exception(f"获取关键词 '{keyword}' 的兴趣度数据失败，已重试{max_retries}次: {str(e)}")
            
            # 计算等待时间
            wait_time = get_fibonacci_wait_time(attempt, initial_wait)
            
            # 根据等待时间决定轮换代理的策略
            # 如果等待时间较长，优先尝试更多代理而非长时间等待
            extra_proxy_rotations = min(5, 1 + attempt // 2)  # 随着尝试次数增加，增加额外代理轮换次数
            
            # 添加一些随机性以避免被识别为机器人
            actual_wait = min(wait_time, 60 + random.randint(0, 30)) if attempt < 3 else wait_time
            
            print(f"获取关键词 '{keyword}' 的兴趣度数据失败，正在切换{extra_proxy_rotations}次代理，{actual_wait}秒后重试: {str(e)}")
            
            # 多轮换几次代理
            for _ in range(extra_proxy_rotations):
                proxy_rotator.get_next_proxy()
                
            time.sleep(actual_wait)

def calculate_keyword_class(keyword_data, reference_data):
    # 将字符串转回数值列表用于计算
    keyword_data_list = [float(x) for x in keyword_data]
    reference_data_list = [float(x) for x in reference_data]
    
    # 计算 reference_data 的平均值
    reference_data_average = sum(reference_data_list) / len(reference_data_list)
    
    # 计算 keyword_data 前五项的平均值
    first_five_avg = sum(keyword_data_list[:5]) / 5
    
    # 获取第六项和第七项
    sixth_value = keyword_data_list[5] if len(keyword_data_list) > 5 else 0
    seventh_value = keyword_data_list[6] if len(keyword_data_list) > 6 else 0
    
    # 判断关键词类别
    keyword_class = None
    if first_five_avg < reference_data_average * 0.2:
        if sixth_value > reference_data_average or seventh_value > reference_data_average:
            keyword_class = 'A'
        elif (sixth_value > reference_data_average * 0.5 or seventh_value > reference_data_average * 0.5):
            if first_five_avg < reference_data_average * 0.1:
                keyword_class = 'B'
            else:
                keyword_class = 'C'
    return keyword_class
    
def send_feishu_message(keyword,reference_keyword):
#     curl --location --request POST 'https://open.feishu.cn/open-apis/bot/v2/hook/7e2ea36b-c640-4ce2-8c76-44a0e7d6c44f' \
# --header 'User-Agent: Apifox/1.0.0 (https://apifox.com)' \
# --header 'Content-Type: application/json' \
# --header 'Accept: */*' \
# --header 'Host: open.feishu.cn' \
# --header 'Connection: keep-alive' \
    # --data-raw '{"msg_type":"text","content":{"text":"【XXX】\n 7days: https://www.baidu.com \n 30 days: https://www.baidu.com"}}'
    url = "https://open.feishu.cn/open-apis/bot/v2/hook/7e2ea36b-c640-4ce2-8c76-44a0e7d6c44f"
    headers = {
        "Content-Type": "application/json"
    }
    keyword_title=keyword
    keyword=keyword.replace(' ','%20')
    reference_keyword=reference_keyword.replace(' ','%20')
    data = {
        "msg_type": "text", 
        "content": {
            "text": f"【{keyword_title}】\n 7days: https://trends.google.com/trends/explore?date=now%207-d&q={keyword},{reference_keyword} \n 30 days: https://trends.google.com/trends/explore?date=today%201-m&q={keyword},{reference_keyword}"
        }
    }
    response = requests.post(url, headers=headers, json=data)
    return response.json()

def save_interest_over_time_to_db(interest_over_time_data, task_id: str, keyword: str, reference_keyword: str):
    try:
        # 先计算 keyword_class（使用原始数值列表）
        keyword_class = calculate_keyword_class(interest_over_time_data[0], interest_over_time_data[1])
        if keyword_class != None:
            send_feishu_message(keyword,reference_keyword)
        # 然后将数据转换为字符串以便存储
        keyword_data = ','.join(map(str, interest_over_time_data[0]))
        reference_data = ','.join(map(str, interest_over_time_data[1]))
        
        # 准备更新数据库的参数
        params = [
            keyword_data,          # keyword_interest_over_time
            reference_data,        # reference_keyword_interest_over_time
            reference_keyword, 
            keyword_class,
            task_id,              # task_id
            keyword,         # root_keyword
        ]
        
        # 执行数据库更新操作
        result = worker.execute(
            """
            UPDATE rising_keywords 
            SET keyword_interest_over_time = ?,
                reference_keyword_interest_over_time = ?,
                reference_keyword = ?,
                keyword_class=?
            WHERE task_id = ? 
            AND keyword = ?
            """,
            params
        )
        
        if not result.get('success', False):
            print(f"更新关键词 '{keyword}' 的兴趣度数据失败: {result.get('error', '未知错误')}")
            
        return {
            "success": result.get('success', False),
            "error": result.get('error', None),
            "keyword": keyword,
            "reference_keyword": reference_keyword
        }
            
    except Exception as e:
        error_msg = f"更新关键词 '{keyword}' 的兴趣度数据时发生异常: {str(e)}"
        print(error_msg)
        return {
            "success": False,
            "error": str(e),
            "keyword": keyword,
            "reference_keyword": reference_keyword
        }
def save_rising_df_to_db(rising_df, task_id: str, root_keyword: str):
    # 为每行数据添加task_id和root_keyword
    success_count = 0
    failed_keywords = []
    
    for _, row in rising_df.iterrows():
        try:
            params = [
                task_id,
                row['query'],
                root_keyword,
                None,
                None,
                None
            ]
            
            result = worker.execute(
                "INSERT INTO rising_keywords (task_id, keyword, root_keyword, reference_keyword, keyword_interest_over_time, reference_keyword_interest_over_time) "
                "VALUES (?, ?, ?, ?, ?, ?)",
                params
            )
            
            if result.get('success', False):
                success_count += 1
            else:
                failed_keywords.append(row['query'])
                print(f"插入关键词 '{row['query']}' 失败: {result.get('error', '未知错误')}")
                
        except Exception as e:
            failed_keywords.append(row['query'])
            print(f"插入关键词 '{row['query']}' 时发生异常: {str(e)}")
    
    return {
        "success": len(failed_keywords) == 0,
        "inserted_count": success_count,
        "failed_count": len(failed_keywords),
        "failed_keywords": failed_keywords
    }
def create_task_id():
    from datetime import datetime
    return datetime.now().strftime("%Y%m%d_%H%M%S")

def setup_logger(task_id):
    log_dir = os.path.join(os.path.dirname(__file__), 'logs')
    os.makedirs(log_dir, exist_ok=True)
    log_file = os.path.join(log_dir, f'task_{task_id}.log')
    
    # 配置根日志记录器
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s +0800 - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger('main')

def main():
   # 创建任务
    task_manager = TaskManager(base_url=DBWorkerUrl)
    task_id = task_manager.create_task()
    if not task_id:
        print("failed to create task id")
        return 
    # 设置日志
    logger = setup_logger(task_id)
    logger.info(f"开始新任务，task id: {task_id}")

    task_manager.start_task(task_id)

    # 获取根关键词列表
    root_keywords = get_root_keywords()
    logging.info(f"获取到根关键词列表: {root_keywords}")
    
    # 遍历根关键词列表
    for _, row in root_keywords.iterrows():
        root_keyword = row['keyword']
        related_queries = get_related_by_keyword(root_keyword)
        logger.info(f"找到{root_keyword}的相关查询词: {related_queries}")
        save_rising_df_to_db(related_queries, task_id, root_keyword)
        time.sleep(5)

    rising_keywords = get_rising_keywords(task_id)
    reference_word = get_config_value("reference_keywords","animal generator")
    
    for keyword in rising_keywords:
        logging.info(f"正在获取{keyword}的兴趣度数据...")
        data = get_interest_over_time_by_word(keyword, reference_word)
        logging.info(f"找到{keyword}的兴趣度数据{data}")
        result = save_interest_over_time_to_db(data, task_id, keyword, reference_word)
        logging.info(f"保存结果: {result}")
        time.sleep(5)

    # 结束任务
    task_manager.complete_task(task_id)
    logging.info("任务完成")

if __name__ == "__main__": 
    main()