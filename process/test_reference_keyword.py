#!/usr/bin/env python3
"""
测试基准词数据

检查基准词 "animal generator" 的数据是否正常
"""

from trends_api import get_interest_over_time_by_word
import logging

# 设置日志
logging.basicConfig(level=logging.DEBUG)
logging.getLogger('urllib3.connectionpool').setLevel(logging.WARNING)

def test_reference_keyword():
    """测试基准词数据"""
    reference_keyword = "animal generator"
    test_keyword = "anime final strike codes"  # 使用一个测试关键词
    
    print(f"测试基准词: {reference_keyword}")
    print(f"测试关键词: {test_keyword}")
    
    try:
        # 获取数据
        interest_data = get_interest_over_time_by_word(test_keyword, reference_keyword)
        
        keyword_data = interest_data[0]
        reference_data = interest_data[1]
        
        print(f"\n关键词数据: {keyword_data}")
        print(f"基准词数据: {reference_data}")
        
        # 分析数据
        keyword_sum = sum([int(round(float(x))) for x in keyword_data])
        reference_sum = sum([int(round(float(x))) for x in reference_data])
        
        print(f"\n关键词总和: {keyword_sum}")
        print(f"基准词总和: {reference_sum}")
        
        if reference_sum == 0:
            print("❌ 基准词数据全为0！这是问题所在。")
            print("建议：")
            print("1. 更换基准词为更热门的词")
            print("2. 或者调整计算逻辑处理0值情况")
        else:
            print("✅ 基准词数据正常")
            
    except Exception as e:
        print(f"测试失败: {e}")

if __name__ == "__main__":
    test_reference_keyword()
