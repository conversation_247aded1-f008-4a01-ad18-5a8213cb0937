import os
import logging
import random
import time
from datetime import datetime
import pytz
from db_ops import get_config_value
from logging.handlers import TimedRotatingFileHandler

def get_fibonacci_wait_time(n, initial_wait):
    """计算优化版的斐波那契等待时间，更适合网络重试场景"""
    max_wait = int(get_config_value('max_wait', 5400))
    
    # 对前几次重试使用较小的等待时间
    if n <= 0:
        return min(initial_wait, max_wait)
    elif n == 1:
        return min(initial_wait * 1.5, max_wait)  # 第二次稍微增加一点等待时间
    elif n == 2:
        return min(initial_wait * 2, max_wait)    # 第三次加倍等待时间
    
    # 从第四次开始使用修改版的斐波那契序列
    # 这样可以让等待时间增长更平滑
    a, b = 2, 3  # 从2,3开始而不是1,1
    for _ in range(3, n + 1):
        a, b = b, a + b
    
    # 添加一点随机性来避免可预测的模式
    jitter = random.uniform(0.8, 1.2)  # 增加±20%的随机波动
    return min(initial_wait * b * jitter, max_wait)

class BeijingFormatter(logging.Formatter):
    """自定义日志格式化器，强制使用北京时间"""
    
    def formatTime(self, record, datefmt=None):
        beijing_tz = pytz.timezone('Asia/Shanghai')
        dt = datetime.fromtimestamp(record.created).astimezone(beijing_tz)
        if datefmt:
            return dt.strftime(datefmt)
        return dt.strftime("%Y-%m-%d %H:%M:%S")

def setup_logger(task_id):
    """设置日志记录器"""
    log_dir = os.path.join(os.path.dirname(__file__), 'logs')
    os.makedirs(log_dir, exist_ok=True)
    log_file = os.path.join(log_dir, f'task_{task_id}.log')
    
    # 创建自定义的北京时间格式化器
    beijing_formatter = BeijingFormatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 配置根日志记录器
    logging.basicConfig(
        level=logging.INFO,
        handlers=[
            TimedRotatingFileHandler(log_file, when='midnight', interval=1, backupCount=7, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    # 为所有处理器设置北京时间格式化器
    for handler in logging.getLogger().handlers:
        handler.setFormatter(beijing_formatter)
        
    return logging.getLogger('main')

def create_task_id():
    """创建任务ID，基于当前时间"""
    return datetime.now().strftime("%Y%m%d_%H%M%S")

def get_beijing_time():
    """获取北京时间"""
    beijing_tz = pytz.timezone('Asia/Shanghai')
    return datetime.now(beijing_tz) 