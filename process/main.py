#!/usr/bin/env python3
"""
Google Trends 关键词挖掘主程序

支持测试模式和生产模式：
- 生产模式：python main.py
- 测试模式：python main.py --max-roots 3 --max-keywords 8 --test-mode

功能包括：
1. 四层筛选模型的数据库写入
2. 绝对搜索量字段的正确保存
3. A级关键词自动加入新生池的功能
4. 批量处理的错误处理机制
"""

import logging
import time
import argparse
from datetime import datetime
from tasks import TaskManager
from db_ops import get_root_keywords, get_config_value, get_pending_keywords_for_batch_processing, save_rising_df_to_db_with_dedup
from trends_api import get_related_by_keyword
from utils import setup_logger
from analysis import process_keywords_batch
from notifier import send_task_completion_notification

def setup_logger_with_mode(test_mode=False):
    """设置日志"""
    if test_mode:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_filename = f"main_test_{timestamp}"
        logger = setup_logger(log_filename)
        # 测试模式下使用详细日志，但抑制urllib3的DEBUG日志
        logging.getLogger().setLevel(logging.DEBUG)
        logging.getLogger('urllib3.connectionpool').setLevel(logging.WARNING)
    else:
        logger = setup_logger("main")
        logging.getLogger('urllib3.connectionpool').setLevel(logging.WARNING)

    return logger

def get_root_keywords_with_limit(max_roots=None, test_mode=False, test_keywords=None):
    """获取根关键词（可选限制数量）"""
    df = get_root_keywords(test_mode, test_keywords)

    if max_roots and len(df) > max_roots:
        # 选择最新的几个词根进行测试
        df = df.head(max_roots)
        logging.info(f"测试模式：限制处理 {max_roots} 个词根")

    return df

def validate_database_writes(task_id, test_mode=False):
    """验证数据库写入结果"""
    if not test_mode:
        return

    print(f"\n{'='*60}")
    print(f"数据库写入验证")
    print(f"{'='*60}")

    from db_ops import worker

    try:
        # 1. 验证rising_keywords表的写入
        print(f"\n📊 验证 rising_keywords 表写入...")
        df = worker.execute_to_df(
            """SELECT keyword, root_keyword, keyword_class,
                      keyword_interest_over_time_absolute_search_volume,
                      created_at
               FROM rising_keywords
               WHERE task_id = ?
               ORDER BY created_at DESC
               LIMIT 10""",
            [task_id]
        )

        if not df.empty:
            print(f"✅ 找到 {len(df)} 条记录")
            for _, row in df.iterrows():
                keyword = row['keyword']
                keyword_class = row['keyword_class']
                absolute_volume = row['keyword_interest_over_time_absolute_search_volume']

                print(f"  - {keyword}: 分类={keyword_class or '未分类'}, "
                      f"绝对搜索量={'已计算' if absolute_volume else '未计算'}")
        else:
            print(f"❌ 未找到任何记录")

        # 2. 验证A级关键词是否加入新生池
        print(f"\n🏊 验证新生池写入...")
        a_keywords = worker.execute_to_df(
            """SELECT keyword FROM rising_keywords
               WHERE task_id = ? AND keyword_class = 'A'""",
            [task_id]
        )

        if not a_keywords.empty:
            a_keyword_list = a_keywords['keyword'].tolist()
            print(f"✅ 找到 {len(a_keyword_list)} 个A级关键词: {a_keyword_list}")

            # 检查是否已加入新生池
            for keyword in a_keyword_list:
                pool_df = worker.execute_to_df(
                    """SELECT pool_status, date_added FROM root_keywords
                       WHERE keyword = ?""",
                    [keyword]
                )

                if not pool_df.empty:
                    pool_status = pool_df.iloc[0]['pool_status']
                    print(f"  - {keyword}: 池状态={pool_status} ✅")
                else:
                    print(f"  - {keyword}: 未找到池记录 ❌")
        else:
            print(f"ℹ️ 本次未发现A级关键词")

        # 3. 验证绝对搜索量字段
        print(f"\n📈 验证绝对搜索量字段...")
        volume_df = worker.execute_to_df(
            """SELECT keyword, keyword_interest_over_time_absolute_search_volume
               FROM rising_keywords
               WHERE task_id = ?
               AND keyword_interest_over_time_absolute_search_volume IS NOT NULL
               LIMIT 5""",
            [task_id]
        )

        if not volume_df.empty:
            print(f"✅ 找到 {len(volume_df)} 个关键词有绝对搜索量数据")
            for _, row in volume_df.iterrows():
                keyword = row['keyword']
                volume_series = row['keyword_interest_over_time_absolute_search_volume']
                if volume_series:
                    values = volume_series.split(',')
                    peak = max([float(x) for x in values])
                    print(f"  - {keyword}: 峰值={peak}k, 序列长度={len(values)}")
        else:
            print(f"❌ 未找到绝对搜索量数据")

    except Exception as e:
        print(f"❌ 数据库验证失败: {str(e)}")

def main():
    """主程序入口，执行完整的数据获取和分析流程"""
    parser = argparse.ArgumentParser(description='Google Trends 关键词挖掘主程序')
    parser.add_argument('--max-roots', type=int, help='最大处理词根数量（测试模式）')
    parser.add_argument('--max-keywords', type=int, help='最大处理关键词数量（测试模式）')
    parser.add_argument('--test-mode', action='store_true', help='启用测试模式（详细日志和验证）')
    parser.add_argument('--test-keywords', nargs='+', help='直接测试的关键词列表，跳过根关键词阶段（测试模式）')
    parser.add_argument('--skip-notification', action='store_true', help='跳过任务完成通知')

    args = parser.parse_args()

    # 创建任务
    if args.test_mode:
        # 测试模式：使用模拟任务ID
        task_id = "test_task_" + datetime.now().strftime("%Y%m%d_%H%M%S")
        logging.info(f"测试模式：使用模拟任务ID: {task_id}")
    else:
        task_manager = TaskManager()
        task_id = task_manager.create_task()

        if not task_id:
            print("创建任务ID失败")
            return

    # 设置日志
    logger = setup_logger_with_mode(args.test_mode)

    if args.test_mode:
        logger.info(f"🧪 测试模式启动")
        logger.info(f"   最大词根数: {args.max_roots or '无限制'}")
        logger.info(f"   最大关键词数: {args.max_keywords or '无限制'}")

    logger.info(f"开始新任务，task id: {task_id}")

    # 开始任务
    if not args.test_mode:
        task_manager.start_task(task_id)

    try:
        # 如果有测试关键词，跳过根关键词阶段
        if args.test_mode and args.test_keywords:
            logging.info(f"测试模式：跳过根关键词阶段，直接处理指定的 {len(args.test_keywords)} 个测试关键词")
        else:
            # 获取根关键词列表（可能限制数量）
            root_keywords = get_root_keywords_with_limit(args.max_roots, False, None)
            logging.info(f"获取到{len(root_keywords)}个根关键词")

            # 遍历根关键词列表，获取相关上升查询
            for _, row in root_keywords.iterrows():
                root_keyword = row['keyword']
                logging.info(f"正在处理根关键词: {root_keyword}")

                try:
                    related_queries = get_related_by_keyword(root_keyword)
                    logger.info(f"找到{root_keyword}的相关查询词: {len(related_queries)}个")
                    save_result = save_rising_df_to_db_with_dedup(related_queries, task_id, root_keyword, args.test_mode)
                    logger.info(f"保存结果: 成功{save_result['inserted_count']}条，失败{save_result['failed_count']}条，去重{save_result['duplicate_count']}条")
                except Exception as e:
                    logger.error(f"处理根关键词 {root_keyword} 时出错: {str(e)}")

                # 请求间隔
                request_interval = int(get_config_value('request_interval', 5))
                time.sleep(request_interval)

        # 使用批量处理模式处理上升关键词
        reference_keyword = get_config_value("reference_keywords", "animal generator")
        logger.info(f"开始批量处理上升关键词，使用参考关键词: {reference_keyword}")

        # 统计信息
        stats = {
            'total': 0,
            'successful': 0,
            'failed': 0,
            'batches_processed': 0,
            'a_grade_keywords': []  # 记录A级关键词
        }

        # 批量处理关键词
        batch_size = 4  # 每批处理4个关键词
        processed_keywords = 0
        test_processed_keywords = set()  # 测试模式下跟踪已处理的关键词

        while True:
            # 检查是否达到最大关键词限制
            if args.max_keywords and processed_keywords >= args.max_keywords:
                logger.info(f"达到最大关键词限制 ({args.max_keywords})，停止处理")
                break

            # 获取待处理的关键词批次
            remaining_limit = None
            if args.max_keywords:
                remaining_limit = min(batch_size, args.max_keywords - processed_keywords)
            else:
                remaining_limit = batch_size

            if args.test_mode and args.test_keywords:
                # 测试模式：使用指定的测试关键词
                keywords_batch = get_pending_keywords_for_batch_processing(task_id, remaining_limit, args.test_mode, args.test_keywords, test_processed_keywords)
            else:
                # 正常模式：从数据库获取待处理关键词
                keywords_batch = get_pending_keywords_for_batch_processing(task_id, remaining_limit, False, None)

            if not keywords_batch:
                logger.info("没有更多待处理的关键词")
                break

            logger.info(f"开始处理第{stats['batches_processed'] + 1}批，包含{len(keywords_batch)}个关键词")

            try:
                # 添加批次数据验证
                logging.debug(f"批次数据验证: {keywords_batch}")
                for i, item in enumerate(keywords_batch):
                    if not isinstance(item, dict):
                        logging.error(f"批次第{i+1}项数据类型错误: {type(item)} - {item}")
                    elif 'keyword' not in item or 'root_keyword' not in item:
                        logging.error(f"批次第{i+1}项缺少必要字段: {item}")

                # 批量处理关键词
                batch_result = process_keywords_batch(keywords_batch, task_id, reference_keyword, args.test_mode)

                # 更新统计信息
                stats['total'] += batch_result.get('processed', 0)
                stats['successful'] += batch_result.get('successful', 0)
                stats['failed'] += batch_result.get('failed', 0)
                stats['batches_processed'] += 1

                # 记录A级关键词
                for detail in batch_result.get('details', []):
                    if detail.get('class') == 'A':
                        stats['a_grade_keywords'].append(detail.get('keyword'))

                logger.info(f"第{stats['batches_processed']}批处理完成: "
                           f"处理{batch_result.get('processed', 0)}个，"
                           f"成功{batch_result.get('successful', 0)}个，"
                           f"失败{batch_result.get('failed', 0)}个")

                # 测试模式下，将处理过的关键词添加到已处理集合
                if args.test_mode and args.test_keywords:
                    for item in keywords_batch:
                        if isinstance(item, dict) and 'keyword' in item:
                            test_processed_keywords.add(item['keyword'])

            except Exception as e:
                import traceback
                error_detail = traceback.format_exc()
                logger.error(f"批量处理第{stats['batches_processed'] + 1}批时出错: {str(e)}")
                logger.error(f"详细错误信息: {error_detail}")
                logger.error(f"出错时的批次数据: {keywords_batch}")

                # 如果批量处理失败，将该批次的所有关键词标记为失败
                stats['total'] += len(keywords_batch)
                stats['failed'] += len(keywords_batch)
                stats['batches_processed'] += 1

                # 测试模式下，即使失败也要将关键词标记为已处理
                if args.test_mode and args.test_keywords:
                    for item in keywords_batch:
                        if isinstance(item, dict) and 'keyword' in item:
                            test_processed_keywords.add(item['keyword'])

            # 批次间隔
            request_interval = int(get_config_value('request_interval', 5))
            time.sleep(request_interval)

        # 结束任务
        if not args.test_mode:
            task_manager.complete_task(task_id)

        logging.info(f"任务完成，共处理{stats['batches_processed']}批次，"
                    f"处理了{stats['total']}个关键词，成功{stats['successful']}个，失败{stats['failed']}个")

        if stats['a_grade_keywords']:
            logging.info(f"发现 {len(stats['a_grade_keywords'])} 个A级关键词: {stats['a_grade_keywords']}")

        # 发送任务完成通知
        if not args.test_mode:
            send_task_completion_notification(task_id, stats)
        
    except Exception as e:
        # 记录错误
        logging.error(f"任务执行过程中发生错误: {str(e)}")
        # 尝试标记任务为失败
        if not args.test_mode:
            try:
                task_manager.fail_task(task_id, str(e))
            except:
                pass
        raise

if __name__ == "__main__": 
    main()